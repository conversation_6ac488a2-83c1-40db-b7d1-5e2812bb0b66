const fs = require('fs');
const path = require('path');
const glob = require('glob');

function generateReportsList() {
  const reportsDir = path.join(__dirname, 'reports');
  const outputFile = path.join(__dirname, 'reports', 'reports.json');
  
  // Find all HTML reports
  const files = glob.sync(path.join(reportsDir, '**/*.html'));
  
  const reports = {
    lastUpdated: new Date().toISOString(),
    yearly: [],
    monthly: {}
  };

  files.forEach(file => {
    const filename = path.basename(file);
    const matchYearly = filename.match(/^yearly_report_(\d{4})\.html$/);
    const matchMonthly = filename.match(/^monthly_report_(\d{4})_(\d{2})\.html$/);

    if (matchYearly) {
      const year = parseInt(matchYearly[1], 10);
      if (!reports.yearly.includes(year)) {
        reports.yearly.push(year);
      }
    } else if (matchMonthly) {
      const year = parseInt(matchMonthly[1], 10);
      const month = parseInt(matchMonthly[2], 10);
      
      if (!reports.monthly[year]) {
        reports.monthly[year] = [];
      }
      if (!reports.monthly[year].includes(month)) {
        reports.monthly[year].push(month);
      }
    }
  });

  // Sort years in descending order
  reports.yearly.sort((a, b) => b - a);
  
  // Sort months in ascending order for each year
  Object.values(reports.monthly).forEach(months => months.sort((a, b) => a - b));

  // Write the reports list to a JSON file
  fs.writeFileSync(outputFile, JSON.stringify(reports, null, 2));
  console.log(`Generated ${outputFile} with ${reports.yearly.length} yearly and ${Object.keys(reports.monthly).length} monthly reports`);
}

generateReportsList();
