<!DOCTYPE html>
<html>
<head>
  <title>Monthly Report - {{month}}/{{year}}</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .chart-container {
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 500px;
      width: 100%;
    }

    @media (max-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
    .daily-section,
    .payment-section {
      display: flex;
      gap: 30px;
      margin: 20px 0;
      align-items: flex-start;
    }
    .daily-chart,
    .payment-chart {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 600px;
    }
    .daily-table-container,
    .payment-table-container {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      overflow-x: auto;
    }
    .daily-table-container {
      overflow-x: auto; /* Allow horizontal scrolling for wide tables */
    }
    .daily-table,
    .payment-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-size: 0.9em;
    }
    .daily-table {
      font-size: 0.8em; /* Smaller font for more columns */
    }
    .daily-table th,
    .daily-table td,
    .payment-table th,
    .payment-table td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #eee;
    }
    .daily-table th,
    .payment-table th {
      background: #3498db;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }
    .daily-table th:nth-child(n+2),
    .payment-table th:nth-child(n+2) {
      text-align: right;
    }
    .daily-table tr:hover,
    .payment-table tr:hover {
      background: #f8f9fa;
    }
    .daily-table tr:last-child td,
    .payment-table tr:last-child td {
      border-bottom: none;
    }
    .payment-amount {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }
    .payment-percentage {
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-method-name {
      font-weight: 500;
      color: #34495e;
    }
    .table-total {
      background: #ecf0f1 !important;
      font-weight: 600;
      border-top: 2px solid #3498db;
    }
    .summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 1.1em;
    }
    .summary-value {
      font-weight: bold;
      color: #2c3e50;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #7f8c8d;
      font-size: 0.9em;
    }

    /* Toggle Controls */
    .toggle-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }

    .toggle-control {
      display: inline-flex;
      background: #ecf0f1;
      border-radius: 25px;
      padding: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .toggle-button {
      padding: 8px 20px;
      border: none;
      background: transparent;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      color: #7f8c8d;
    }

    .toggle-button.active {
      background: #3498db;
      color: white;
      box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    }

    .toggle-button:hover:not(.active) {
      color: #2c3e50;
    }

    /* Hide/Show functionality */
    .chart-view .table-container,
    .table-view .chart-container-section {
      display: none;
    }

    .chart-view .chart-container-section,
    .table-view .table-container {
      display: block;
      flex: 1;
    }

    /* Full width when only one view is shown */
    .section-content.chart-view .chart-container-section,
    .section-content.table-view .table-container {
      width: 100%;
      max-width: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Monthly Sales Report - {{month}}/{{year}}</h1>
      <p>Generated on {{generated_date}}</p>
    </div>

    <div class="summary">
      <h2>Summary</h2>
      <div id="summary-content">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>

    <div class="daily-section">
      <div class="toggle-container">
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('daily', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('daily', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="daily-content">
        <div class="chart-container-section daily-chart">
          <h2>Daily Sales - {{month}}/{{year}}</h2>
          <div style="position: relative; height: calc(100% - 40px); width: 100%;">
            <canvas id="dailySalesChart"></canvas>
          </div>
        </div>

        <div class="table-container daily-table-container">
          <h2>Daily Sales Breakdown</h2>
          <table class="daily-table" id="dailySalesTable">
            <thead id="dailyTableHeader">
              <!-- Will be populated by JavaScript -->
            </thead>
            <tbody id="dailyTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="payment-section">
      <div class="toggle-container">
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('payment', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('payment', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="payment-content">
        <div class="chart-container-section payment-chart">
          <h2>Payment Methods - {{month}}/{{year}}</h2>
          <div style="position: relative; height: calc(100% - 40px); width: 100%;">
            <canvas id="paymentMethodsChart"></canvas>
          </div>
        </div>

        <div class="table-container payment-table-container">
          <h2>Payment Methods Breakdown</h2>
          <table class="payment-table" id="paymentMethodsTable">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Percentage</th>
              </tr>
            </thead>
            <tbody id="paymentTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <h2>Hourly Distribution - {{month}}/{{year}}</h2>
      <div style="position: relative; height: calc(100% - 40px); width: 100%;">
        <canvas id="hourlyDistributionChart"></canvas>
      </div>
    </div>

    <div class="footer">
      <p>SnP Report Generator | Generated by Asia Club Management System</p>
    </div>
  </div>

  <script>
    // Parse the JSON data passed from Pascal
    const monthlyData = JSON.parse('{{monthly_data}}');
    const paymentData = JSON.parse('{{payment_data}}');
    const dailyData = JSON.parse('{{daily_data}}');
    const hourlyData = JSON.parse('{{hourly_data}}');

    // Toggle functionality
    function toggleView(section, view) {
      const contentElement = document.getElementById(section + '-content');
      const buttons = contentElement.parentElement.querySelectorAll('.toggle-button');

      // Update button states
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      // Update content view
      contentElement.className = 'section-content ' + view + '-view';

      // Save preference
      localStorage.setItem(section + '-view', view);
    }

    // Load saved preferences
    function loadViewPreferences() {
      const dailyView = localStorage.getItem('daily-view') || 'table';
      const paymentView = localStorage.getItem('payment-view') || 'table';

      // Set daily section
      const dailyContent = document.getElementById('daily-content');
      const dailyButtons = dailyContent.parentElement.querySelectorAll('.toggle-button');
      dailyContent.className = 'section-content ' + dailyView + '-view';
      dailyButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(dailyView));
      });

      // Set payment section
      const paymentContent = document.getElementById('payment-content');
      const paymentButtons = paymentContent.parentElement.querySelectorAll('.toggle-button');
      paymentContent.className = 'section-content ' + paymentView + '-view';
      paymentButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(paymentView));
      });
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2
      }).format(amount);
    }
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      try {
        // Daily Sales Chart
        const dailyCtx = document.getElementById('dailySalesChart');
        if (dailyCtx) {
          // Prepare data for multiple series
          const datasets = [];

          // Check if we have breakdown data
          if (dailyData.cash && dailyData.nonCash && dailyData.discounts) {
            // Show 4 series: Total, Cash, Non-Cash, Discounts
            datasets.push({
              label: 'Total Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Cash',
              data: dailyData.cash || [],
              backgroundColor: 'rgba(75, 192, 192, 0.8)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Non-Cash',
              data: dailyData.nonCash || [],
              backgroundColor: 'rgba(255, 159, 64, 0.8)',
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Discounts',
              data: dailyData.discounts || [],
              backgroundColor: 'rgba(255, 99, 132, 0.8)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2
            });
          } else {
            // Fallback to single series
            datasets.push({
              label: 'Daily Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
          }

          new Chart(dailyCtx, {
            type: 'bar',
            data: {
              labels: dailyData.dates || [],
              datasets: datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return formatCurrency(context.raw);
                    }
                  }
                },
                legend: {
                  display: datasets.length > 1
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Daily Sales Table
        const dailyTableBody = document.getElementById('dailyTableBody');
        const dailyTableHeader = document.getElementById('dailyTableHeader');
        if (dailyTableBody && dailyTableHeader && dailyData.dates && dailyData.amounts) {
          const paymentMethods = dailyData.paymentMethods || [];
          const dailyBreakdown = dailyData.dailyBreakdown || [];

          // Clear existing content
          dailyTableBody.innerHTML = '';
          dailyTableHeader.innerHTML = '';

          // Create header row
          const headerRow = document.createElement('tr');
          headerRow.innerHTML = `
            <th>Day</th>
            <th>Total Sales</th>
            <th>Discounts</th>
            <th>% of Month</th>
            ${paymentMethods.map(method => `<th>${method}</th>`).join('')}
          `;
          dailyTableHeader.appendChild(headerRow);

          const monthTotal = dailyData.amounts.reduce((sum, amount) => sum + amount, 0);

          // Add rows for each day
          dailyData.dates.forEach((date, index) => {
            const amount = dailyData.amounts[index] || 0;
            const discount = (dailyData.discounts && dailyData.discounts[index]) || 0;
            const percentage = monthTotal > 0 ? (amount / monthTotal * 100).toFixed(1) : '0.0';
            const dayNumber = index + 1; // Convert to 1-based day

            // Find payment breakdown for this day
            const dayBreakdown = dailyBreakdown.find(db => db.day === dayNumber);
            const payments = dayBreakdown ? dayBreakdown.payments : {};

            const displayAmount = amount > 0 ? formatCurrency(amount) : '';
            const displayDiscount = discount > 0 ? formatCurrency(discount) : '';
            const displayPercentage = amount > 0 ? percentage + '%' : '';

            // Generate payment method cells
            let paymentCells = '';
            paymentMethods.forEach(method => {
              const methodAmount = payments[method] || 0;
              const displayMethodAmount = methodAmount > 0 ? formatCurrency(methodAmount) : '';
              paymentCells += `<td class="payment-amount">${displayMethodAmount}</td>`;
            });

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${date}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-amount">${displayDiscount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
              ${paymentCells}
            `;
            dailyTableBody.appendChild(row);
          });

          // Add total row
          if (monthTotal > 0) {
            // Calculate totals for each column
            const totalDiscounts = (dailyData.discounts || []).reduce((sum, amount) => sum + amount, 0);

            // Calculate payment method totals
            let paymentTotalCells = '';
            paymentMethods.forEach(method => {
              let methodTotal = 0;
              dailyBreakdown.forEach(db => {
                methodTotal += db.payments[method] || 0;
              });
              paymentTotalCells += `<td class="payment-amount"><strong>${formatCurrency(methodTotal)}</strong></td>`;
            });

            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(monthTotal)}</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalDiscounts)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
              ${paymentTotalCells}
            `;
            dailyTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing daily sales chart:', error);
      }

      // Payment Methods Chart
      try {
        const paymentCtx = document.getElementById('paymentMethodsChart');
        if (paymentCtx) {
          new Chart(paymentCtx, {
            type: 'pie',
            data: {
              labels: Object.keys(paymentData.methods || {}),
              datasets: [{
                data: Object.values(paymentData.methods || {}),
                backgroundColor: [
                  '#3498db', '#2ecc71', '#e74c3c', '#f1c40f',
                  '#9b59b6', '#1abc9c', '#e67e22', '#74c0fc'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Payment Methods Distribution',
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = Math.round((value / total) * 100);
                      return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Payment Methods Table
        const paymentTableBody = document.getElementById('paymentTableBody');
        if (paymentTableBody && paymentData.methods) {
          const methods = paymentData.methods;
          const methodNames = Object.keys(methods);
          const methodAmounts = Object.values(methods);

          // Calculate total for percentages
          const totalAmount = methodAmounts.reduce((sum, amount) => sum + amount, 0);

          // Sort methods by amount (descending)
          const sortedMethods = methodNames
            .map(name => ({ name, amount: methods[name] }))
            .sort((a, b) => b.amount - a.amount);

          // Clear existing content
          paymentTableBody.innerHTML = '';

          // Add rows for each payment method
          sortedMethods.forEach(method => {
            const percentage = totalAmount > 0 ? (method.amount / totalAmount * 100).toFixed(1) : '0.0';
            const displayAmount = method.amount > 0 ? formatCurrency(method.amount) : '';
            const displayPercentage = method.amount > 0 ? percentage + '%' : '';

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${method.name}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
            `;
            paymentTableBody.appendChild(row);
          });

          // Add total row
          if (totalAmount > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalAmount)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
            `;
            paymentTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing payment methods chart:', error);
      }

      // Update summary content
      try {
        const summaryContent = document.getElementById('summary-content');
        if (summaryContent && monthlyData) {
          summaryContent.innerHTML =
            '<div class="summary-item">' +
            'Total Sales: <span class="summary-value">' + formatCurrency(monthlyData.totalSales || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Average Daily Sales: <span class="summary-value">' + formatCurrency(monthlyData.averageDaily || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Busiest Day: <span class="summary-value">' +
            (monthlyData.busiestDay ? monthlyData.busiestDay.day + ' (' + formatCurrency(monthlyData.busiestDay.amount) + ')' : 'N/A') +
            '</span></div>';
        }
      } catch (error) {
        console.error('Error updating summary content:', error);
      }

      // Hourly Distribution Chart
      try {
        const hourlyCtx = document.getElementById('hourlyDistributionChart');
        if (hourlyCtx) {
          new Chart(hourlyCtx.getContext('2d'), {
            type: 'line',
            data: {
              labels: Array.from({length: 24}, (_, i) => `${i}:00`),
              datasets: [{
                label: 'Average Sales',
                data: hourlyData.hours ? hourlyData.hours.map(hour => hour.average) : [],
                borderColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return `Avg: ${formatCurrency(context.raw)}`;
                    }
                  }
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }
      } catch (error) {
        console.error('Error initializing hourly distribution chart:', error);
      }
    }); // Close DOMContentLoaded
  </script>
</body>
</html>
