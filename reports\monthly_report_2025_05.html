<!DOCTYPE html>
<html>
<head>
  <title>Monthly Report - 5/2025</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .chart-container {
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 500px;
      width: 100%;
    }

    @media (max-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
    .daily-section,
    .payment-section {
      display: flex;
      gap: 30px;
      margin: 20px 0;
      align-items: flex-start;
    }
    .daily-chart,
    .payment-chart {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 600px;
    }
    .daily-table-container,
    .payment-table-container {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      overflow-x: auto;
    }
    .daily-table-container {
      overflow-x: auto; /* Allow horizontal scrolling for wide tables */
    }
    .daily-table,
    .payment-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-size: 0.9em;
    }
    .daily-table {
      font-size: 0.8em; /* Smaller font for more columns */
    }
    .daily-table th,
    .daily-table td,
    .payment-table th,
    .payment-table td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #eee;
    }
    .daily-table th,
    .payment-table th {
      background: #3498db;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }
    .daily-table th:nth-child(n+2),
    .payment-table th:nth-child(n+2) {
      text-align: right;
    }
    .daily-table tr:hover,
    .payment-table tr:hover {
      background: #f8f9fa;
    }
    .daily-table tr:last-child td,
    .payment-table tr:last-child td {
      border-bottom: none;
    }
    .payment-amount {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }
    .payment-percentage {
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-method-name {
      font-weight: 500;
      color: #34495e;
    }
    .table-total {
      background: #ecf0f1 !important;
      font-weight: 600;
      border-top: 2px solid #3498db;
    }
    .summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 1.1em;
    }
    .summary-value {
      font-weight: bold;
      color: #2c3e50;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #7f8c8d;
      font-size: 0.9em;
    }

    /* Toggle Controls */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 20px;
    }

    .section-title {
      margin: 0;
      font-size: 1.5em;
      color: #2c3e50;
    }

    .toggle-control {
      display: inline-flex;
      background: #ecf0f1;
      border-radius: 20px;
      padding: 3px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .toggle-button {
      padding: 6px 16px;
      border: none;
      background: transparent;
      border-radius: 17px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.3s ease;
      color: #7f8c8d;
    }

    .toggle-button.active {
      background: #3498db;
      color: white;
      box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    }

    .toggle-button:hover:not(.active) {
      color: #2c3e50;
    }

    /* Hide/Show functionality */
    .chart-view .table-container,
    .table-view .chart-container-section {
      display: none;
    }

    .chart-view .chart-container-section,
    .table-view .table-container {
      display: block;
      flex: 1;
    }

    /* Full width when only one view is shown */
    .section-content.chart-view .chart-container-section,
    .section-content.table-view .table-container {
      width: 100%;
      max-width: none;
    }

    /* Chart container sizing fixes */
    .chart-container-section {
      position: relative;
      height: 600px;
      max-height: 600px;
      overflow: hidden;
    }

    .chart-container-section canvas {
      max-width: 100% !important;
      max-height: 100% !important;
    }

    @media (max-width: 768px) {
      .section-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }

      .chart-container-section {
        height: 400px;
        max-height: 400px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Monthly Sales Report - 5/2025</h1>
      <p>Generated on 2025-08-17 00:25:39</p>
    </div>

    <div class="summary">
      <h2>Summary</h2>
      <div id="summary-content">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>

    <div class="daily-section">
      <div class="section-header">
        <h2 class="section-title">Daily Sales - 5/2025</h2>
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('daily', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('daily', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="daily-content">
        <div class="chart-container-section daily-chart">
          <div style="position: relative; height: calc(100% - 20px); width: 100%;">
            <canvas id="dailySalesChart"></canvas>
          </div>
        </div>

        <div class="table-container daily-table-container">
          <table class="daily-table" id="dailySalesTable">
            <thead id="dailyTableHeader">
              <!-- Will be populated by JavaScript -->
            </thead>
            <tbody id="dailyTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="payment-section">
      <div class="section-header">
        <h2 class="section-title">Payment Methods - 5/2025</h2>
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('payment', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('payment', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="payment-content">
        <div class="chart-container-section payment-chart">
          <div style="position: relative; height: calc(100% - 20px); width: 100%;">
            <canvas id="paymentMethodsChart"></canvas>
          </div>
        </div>

        <div class="table-container payment-table-container">
          <table class="payment-table" id="paymentMethodsTable">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Percentage</th>
              </tr>
            </thead>
            <tbody id="paymentTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <h2>Hourly Distribution - 5/2025</h2>
      <div style="position: relative; height: calc(100% - 40px); width: 100%;">
        <canvas id="hourlyDistributionChart"></canvas>
      </div>
    </div>

    <div class="footer">
      <p>SnP Report Generator | Generated by Asia Club Management System</p>
    </div>
  </div>

  <script>
    // Parse the JSON data passed from Pascal
    const monthlyData = {
  "year" : 2025,
  "month" : 5,
  "totalSales" : 5.6536849999999999E+004,
  "averageDaily" : 1.8237700000000000E+003,
  "busiestDay" : {
    "day" : 22,
    "amount" : 4.2394399999999996E+003
  }
};
    const paymentData = {
  "methods" : {
    "Cash" : 2.0638099999999999E+004,
    "QR Code" : 3.5839029999999999E+004,
    "CN" : 5.9719999999999999E+001
  }
};
    const dailyData = {
  "dates" : [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31"
  ],
  "amounts" : [
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    3.3913499999999995E+003,
    3.0705399999999991E+003,
    3.6790199999999982E+003,
    3.3403100000000018E+003,
    3.5755899999999992E+003,
    3.2470800000000013E+003,
    2.9759199999999978E+003,
    4.2394399999999987E+003,
    3.5406399999999999E+003,
    2.8459200000000005E+003,
    2.6451000000000004E+003,
    3.0018299999999999E+003,
    3.3355599999999981E+003,
    3.2954299999999976E+003,
    4.0276300000000010E+003,
    2.8705799999999999E+003,
    3.4549100000000008E+003
  ],
  "discounts" : [
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    4.9305999999999983E+002,
    3.3273999999999995E+002,
    3.6452999999999997E+002,
    3.1351000000000005E+002,
    4.3716000000000008E+002,
    5.1676000000000022E+002,
    3.9266999999999979E+002,
    4.6264999999999986E+002,
    3.3961999999999989E+002,
    3.5384000000000003E+002,
    3.1575000000000000E+002,
    2.4879999999999998E+002,
    3.2918000000000006E+002,
    3.4032999999999993E+002,
    4.1462000000000000E+002,
    3.2038000000000005E+002,
    2.9454000000000002E+002
  ],
  "cash" : [
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    1.2495600000000004E+003,
    1.0778000000000000E+003,
    1.5405900000000004E+003,
    1.1361199999999999E+003,
    1.5592900000000000E+003,
    1.4815400000000000E+003,
    7.2043000000000018E+002,
    1.2730400000000000E+003,
    1.5577999999999995E+003,
    9.6351999999999998E+002,
    1.1025400000000000E+003,
    1.4385800000000002E+003,
    7.6928000000000009E+002,
    9.5332000000000005E+002,
    1.7149799999999998E+003,
    8.9204999999999995E+002,
    1.2076599999999999E+003
  ],
  "nonCash" : [
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    2.1417899999999995E+003,
    1.9927399999999998E+003,
    2.1384300000000007E+003,
    2.2041899999999996E+003,
    2.0163000000000000E+003,
    1.7655400000000004E+003,
    2.2554899999999984E+003,
    2.9663999999999996E+003,
    1.9828399999999995E+003,
    1.8823999999999996E+003,
    1.5425599999999997E+003,
    1.5632499999999998E+003,
    2.5662799999999993E+003,
    2.3421099999999997E+003,
    2.3126499999999996E+003,
    1.9785299999999997E+003,
    2.2472499999999995E+003
  ],
  "paymentMethods" : [
    "Cash",
    "QR Code",
    "CN"
  ],
  "dailyBreakdown" : [
    {
      "day" : 1,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 2,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 3,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 4,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 5,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 6,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 7,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 8,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 9,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 10,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 11,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 12,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 13,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 14,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "Cash" : 0.0000000000000000E+000,
        "QR Code" : 0.0000000000000000E+000,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 15,
      "total" : 3.3913499999999995E+003,
      "discounts" : 4.9305999999999983E+002,
      "cash" : 1.2495600000000004E+003,
      "nonCash" : 2.1417899999999995E+003,
      "payments" : {
        "Cash" : 1.2495600000000004E+003,
        "QR Code" : 2.1055200000000000E+003,
        "CN" : 3.6270000000000003E+001
      }
    },
    {
      "day" : 16,
      "total" : 3.0705399999999991E+003,
      "discounts" : 3.3273999999999995E+002,
      "cash" : 1.0778000000000000E+003,
      "nonCash" : 1.9927399999999998E+003,
      "payments" : {
        "Cash" : 1.0778000000000000E+003,
        "QR Code" : 1.9927399999999998E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 17,
      "total" : 3.6790199999999982E+003,
      "discounts" : 3.6452999999999997E+002,
      "cash" : 1.5405900000000004E+003,
      "nonCash" : 2.1384300000000007E+003,
      "payments" : {
        "Cash" : 1.5405900000000004E+003,
        "QR Code" : 2.1384300000000007E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 18,
      "total" : 3.3403100000000018E+003,
      "discounts" : 3.1351000000000005E+002,
      "cash" : 1.1361199999999999E+003,
      "nonCash" : 2.2041899999999996E+003,
      "payments" : {
        "Cash" : 1.1361199999999999E+003,
        "QR Code" : 2.2041899999999996E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 19,
      "total" : 3.5755899999999992E+003,
      "discounts" : 4.3716000000000008E+002,
      "cash" : 1.5592900000000000E+003,
      "nonCash" : 2.0163000000000000E+003,
      "payments" : {
        "Cash" : 1.5592900000000000E+003,
        "QR Code" : 2.0163000000000000E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 20,
      "total" : 3.2470800000000013E+003,
      "discounts" : 5.1676000000000022E+002,
      "cash" : 1.4815400000000000E+003,
      "nonCash" : 1.7655400000000004E+003,
      "payments" : {
        "Cash" : 1.4815400000000000E+003,
        "QR Code" : 1.7655400000000004E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 21,
      "total" : 2.9759199999999978E+003,
      "discounts" : 3.9266999999999979E+002,
      "cash" : 7.2043000000000018E+002,
      "nonCash" : 2.2554899999999984E+003,
      "payments" : {
        "Cash" : 7.2043000000000018E+002,
        "QR Code" : 2.2554899999999984E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 22,
      "total" : 4.2394399999999987E+003,
      "discounts" : 4.6264999999999986E+002,
      "cash" : 1.2730400000000000E+003,
      "nonCash" : 2.9663999999999996E+003,
      "payments" : {
        "Cash" : 1.2730400000000000E+003,
        "QR Code" : 2.9663999999999996E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 23,
      "total" : 3.5406399999999999E+003,
      "discounts" : 3.3961999999999989E+002,
      "cash" : 1.5577999999999995E+003,
      "nonCash" : 1.9828399999999995E+003,
      "payments" : {
        "Cash" : 1.5577999999999995E+003,
        "QR Code" : 1.9828399999999995E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 24,
      "total" : 2.8459200000000005E+003,
      "discounts" : 3.5384000000000003E+002,
      "cash" : 9.6351999999999998E+002,
      "nonCash" : 1.8823999999999996E+003,
      "payments" : {
        "Cash" : 9.6351999999999998E+002,
        "QR Code" : 1.8823999999999996E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 25,
      "total" : 2.6451000000000004E+003,
      "discounts" : 3.1575000000000000E+002,
      "cash" : 1.1025400000000000E+003,
      "nonCash" : 1.5425599999999997E+003,
      "payments" : {
        "Cash" : 1.1025400000000000E+003,
        "QR Code" : 1.5425599999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 26,
      "total" : 3.0018299999999999E+003,
      "discounts" : 2.4879999999999998E+002,
      "cash" : 1.4385800000000002E+003,
      "nonCash" : 1.5632499999999998E+003,
      "payments" : {
        "Cash" : 1.4385800000000002E+003,
        "QR Code" : 1.5632499999999998E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 27,
      "total" : 3.3355599999999981E+003,
      "discounts" : 3.2918000000000006E+002,
      "cash" : 7.6928000000000009E+002,
      "nonCash" : 2.5662799999999993E+003,
      "payments" : {
        "Cash" : 7.6928000000000009E+002,
        "QR Code" : 2.5662799999999993E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 28,
      "total" : 3.2954299999999976E+003,
      "discounts" : 3.4032999999999993E+002,
      "cash" : 9.5332000000000005E+002,
      "nonCash" : 2.3421099999999997E+003,
      "payments" : {
        "Cash" : 9.5332000000000005E+002,
        "QR Code" : 2.3421099999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 29,
      "total" : 4.0276300000000010E+003,
      "discounts" : 4.1462000000000000E+002,
      "cash" : 1.7149799999999998E+003,
      "nonCash" : 2.3126499999999996E+003,
      "payments" : {
        "Cash" : 1.7149799999999998E+003,
        "QR Code" : 2.3126499999999996E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 30,
      "total" : 2.8705799999999999E+003,
      "discounts" : 3.2038000000000005E+002,
      "cash" : 8.9204999999999995E+002,
      "nonCash" : 1.9785299999999997E+003,
      "payments" : {
        "Cash" : 8.9204999999999995E+002,
        "QR Code" : 1.9785299999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 31,
      "total" : 3.4549100000000008E+003,
      "discounts" : 2.9454000000000002E+002,
      "cash" : 1.2076599999999999E+003,
      "nonCash" : 2.2472499999999995E+003,
      "payments" : {
        "Cash" : 1.2076599999999999E+003,
        "QR Code" : 2.2237999999999997E+003,
        "CN" : 2.3450000000000003E+001
      }
    }
  ]
};
    const hourlyData = {
  "hours" : [
    {
      "hour" : 0,
      "total" : 4.5620000000000000E+003,
      "count" : 187,
      "average" : 2.4395721925133689E+001
    },
    {
      "hour" : 1,
      "total" : 3.1545500000000006E+003,
      "count" : 128,
      "average" : 2.4644921875000005E+001
    },
    {
      "hour" : 2,
      "total" : 1.4629400000000005E+003,
      "count" : 74,
      "average" : 1.9769459459459465E+001
    },
    {
      "hour" : 3,
      "total" : 4.6587000000000012E+002,
      "count" : 27,
      "average" : 1.7254444444444449E+001
    },
    {
      "hour" : 4,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 5,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 6,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 7,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 8,
      "total" : 2.9600000000000000E+000,
      "count" : 3,
      "average" : 9.8666666666666669E-001
    },
    {
      "hour" : 9,
      "total" : 7.5140000000000001E+001,
      "count" : 6,
      "average" : 1.2523333333333333E+001
    },
    {
      "hour" : 10,
      "total" : 8.1574000000000012E+002,
      "count" : 38,
      "average" : 2.1466842105263161E+001
    },
    {
      "hour" : 11,
      "total" : 1.2414900000000002E+003,
      "count" : 53,
      "average" : 2.3424339622641515E+001
    },
    {
      "hour" : 12,
      "total" : 2.6341700000000005E+003,
      "count" : 87,
      "average" : 3.0277816091954030E+001
    },
    {
      "hour" : 13,
      "total" : 2.0930199999999995E+003,
      "count" : 86,
      "average" : 2.4337441860465109E+001
    },
    {
      "hour" : 14,
      "total" : 3.0950999999999999E+003,
      "count" : 118,
      "average" : 2.6229661016949152E+001
    },
    {
      "hour" : 15,
      "total" : 2.5876400000000003E+003,
      "count" : 115,
      "average" : 2.2501217391304351E+001
    },
    {
      "hour" : 16,
      "total" : 3.8776299999999997E+003,
      "count" : 162,
      "average" : 2.3935987654320986E+001
    },
    {
      "hour" : 17,
      "total" : 3.2608399999999988E+003,
      "count" : 158,
      "average" : 2.0638227848101259E+001
    },
    {
      "hour" : 18,
      "total" : 3.7803099999999990E+003,
      "count" : 151,
      "average" : 2.5035165562913900E+001
    },
    {
      "hour" : 19,
      "total" : 2.6580200000000004E+003,
      "count" : 119,
      "average" : 2.2336302521008406E+001
    },
    {
      "hour" : 20,
      "total" : 4.8985399999999981E+003,
      "count" : 149,
      "average" : 3.2876107382550323E+001
    },
    {
      "hour" : 21,
      "total" : 5.6681099999999979E+003,
      "count" : 206,
      "average" : 2.7515097087378631E+001
    },
    {
      "hour" : 22,
      "total" : 5.5204299999999976E+003,
      "count" : 210,
      "average" : 2.6287761904761894E+001
    },
    {
      "hour" : 23,
      "total" : 4.6823500000000004E+003,
      "count" : 199,
      "average" : 2.3529396984924624E+001
    }
  ]
};

    // Toggle functionality
    function toggleView(section, view) {
      const contentElement = document.getElementById(section + '-content');
      const buttons = contentElement.parentElement.querySelectorAll('.toggle-button');

      // Update button states
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      // Update content view
      contentElement.className = 'section-content ' + view + '-view';

      // Save preference
      localStorage.setItem(section + '-view', view);
    }

    // Load saved preferences
    function loadViewPreferences() {
      const dailyView = localStorage.getItem('daily-view') || 'table';
      const paymentView = localStorage.getItem('payment-view') || 'table';

      // Set daily section
      const dailyContent = document.getElementById('daily-content');
      const dailyButtons = dailyContent.parentElement.querySelectorAll('.toggle-button');
      dailyContent.className = 'section-content ' + dailyView + '-view';
      dailyButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(dailyView));
      });

      // Set payment section
      const paymentContent = document.getElementById('payment-content');
      const paymentButtons = paymentContent.parentElement.querySelectorAll('.toggle-button');
      paymentContent.className = 'section-content ' + paymentView + '-view';
      paymentButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(paymentView));
      });
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2
      }).format(amount);
    }
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Load view preferences first
      loadViewPreferences();

      try {
        // Daily Sales Chart
        const dailyCtx = document.getElementById('dailySalesChart');
        if (dailyCtx) {
          // Prepare data for multiple series
          const datasets = [];

          // Check if we have breakdown data
          if (dailyData.cash && dailyData.nonCash && dailyData.discounts) {
            // Show 4 series: Total, Cash, Non-Cash, Discounts
            datasets.push({
              label: 'Total Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Cash',
              data: dailyData.cash || [],
              backgroundColor: 'rgba(75, 192, 192, 0.8)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Non-Cash',
              data: dailyData.nonCash || [],
              backgroundColor: 'rgba(255, 159, 64, 0.8)',
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Discounts',
              data: dailyData.discounts || [],
              backgroundColor: 'rgba(255, 99, 132, 0.8)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2
            });
          } else {
            // Fallback to single series
            datasets.push({
              label: 'Daily Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
          }

          new Chart(dailyCtx, {
            type: 'bar',
            data: {
              labels: dailyData.dates || [],
              datasets: datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return formatCurrency(context.raw);
                    }
                  }
                },
                legend: {
                  display: datasets.length > 1
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Daily Sales Table
        const dailyTableBody = document.getElementById('dailyTableBody');
        const dailyTableHeader = document.getElementById('dailyTableHeader');
        if (dailyTableBody && dailyTableHeader && dailyData.dates && dailyData.amounts) {
          const paymentMethods = dailyData.paymentMethods || [];
          const dailyBreakdown = dailyData.dailyBreakdown || [];

          // Clear existing content
          dailyTableBody.innerHTML = '';
          dailyTableHeader.innerHTML = '';

          // Create header row
          const headerRow = document.createElement('tr');
          headerRow.innerHTML = `
            <th>Day</th>
            <th>Total Sales</th>
            <th>Discounts</th>
            <th>% of Month</th>
            ${paymentMethods.map(method => `<th>${method}</th>`).join('')}
          `;
          dailyTableHeader.appendChild(headerRow);

          const monthTotal = dailyData.amounts.reduce((sum, amount) => sum + amount, 0);

          // Add rows for each day
          dailyData.dates.forEach((date, index) => {
            const amount = dailyData.amounts[index] || 0;
            const discount = (dailyData.discounts && dailyData.discounts[index]) || 0;
            const percentage = monthTotal > 0 ? (amount / monthTotal * 100).toFixed(1) : '0.0';
            const dayNumber = index + 1; // Convert to 1-based day

            // Find payment breakdown for this day
            const dayBreakdown = dailyBreakdown.find(db => db.day === dayNumber);
            const payments = dayBreakdown ? dayBreakdown.payments : {};

            const displayAmount = amount > 0 ? formatCurrency(amount) : '';
            const displayDiscount = discount > 0 ? formatCurrency(discount) : '';
            const displayPercentage = amount > 0 ? percentage + '%' : '';

            // Generate payment method cells
            let paymentCells = '';
            paymentMethods.forEach(method => {
              const methodAmount = payments[method] || 0;
              const displayMethodAmount = methodAmount > 0 ? formatCurrency(methodAmount) : '';
              paymentCells += `<td class="payment-amount">${displayMethodAmount}</td>`;
            });

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${date}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-amount">${displayDiscount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
              ${paymentCells}
            `;
            dailyTableBody.appendChild(row);
          });

          // Add total row
          if (monthTotal > 0) {
            // Calculate totals for each column
            const totalDiscounts = (dailyData.discounts || []).reduce((sum, amount) => sum + amount, 0);

            // Calculate payment method totals
            let paymentTotalCells = '';
            paymentMethods.forEach(method => {
              let methodTotal = 0;
              dailyBreakdown.forEach(db => {
                methodTotal += db.payments[method] || 0;
              });
              paymentTotalCells += `<td class="payment-amount"><strong>${formatCurrency(methodTotal)}</strong></td>`;
            });

            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(monthTotal)}</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalDiscounts)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
              ${paymentTotalCells}
            `;
            dailyTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing daily sales chart:', error);
      }

      // Payment Methods Chart
      try {
        const paymentCtx = document.getElementById('paymentMethodsChart');
        if (paymentCtx) {
          new Chart(paymentCtx, {
            type: 'pie',
            data: {
              labels: Object.keys(paymentData.methods || {}),
              datasets: [{
                data: Object.values(paymentData.methods || {}),
                backgroundColor: [
                  '#3498db', '#2ecc71', '#e74c3c', '#f1c40f',
                  '#9b59b6', '#1abc9c', '#e67e22', '#74c0fc'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Payment Methods Distribution',
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = Math.round((value / total) * 100);
                      return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Payment Methods Table
        const paymentTableBody = document.getElementById('paymentTableBody');
        if (paymentTableBody && paymentData.methods) {
          const methods = paymentData.methods;
          const methodNames = Object.keys(methods);
          const methodAmounts = Object.values(methods);

          // Calculate total for percentages
          const totalAmount = methodAmounts.reduce((sum, amount) => sum + amount, 0);

          // Sort methods by amount (descending)
          const sortedMethods = methodNames
            .map(name => ({ name, amount: methods[name] }))
            .sort((a, b) => b.amount - a.amount);

          // Clear existing content
          paymentTableBody.innerHTML = '';

          // Add rows for each payment method
          sortedMethods.forEach(method => {
            const percentage = totalAmount > 0 ? (method.amount / totalAmount * 100).toFixed(1) : '0.0';
            const displayAmount = method.amount > 0 ? formatCurrency(method.amount) : '';
            const displayPercentage = method.amount > 0 ? percentage + '%' : '';

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${method.name}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
            `;
            paymentTableBody.appendChild(row);
          });

          // Add total row
          if (totalAmount > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalAmount)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
            `;
            paymentTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing payment methods chart:', error);
      }

      // Update summary content
      try {
        const summaryContent = document.getElementById('summary-content');
        if (summaryContent && monthlyData) {
          summaryContent.innerHTML =
            '<div class="summary-item">' +
            'Total Sales: <span class="summary-value">' + formatCurrency(monthlyData.totalSales || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Average Daily Sales: <span class="summary-value">' + formatCurrency(monthlyData.averageDaily || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Busiest Day: <span class="summary-value">' +
            (monthlyData.busiestDay ? monthlyData.busiestDay.day + ' (' + formatCurrency(monthlyData.busiestDay.amount) + ')' : 'N/A') +
            '</span></div>';
        }
      } catch (error) {
        console.error('Error updating summary content:', error);
      }

      // Hourly Distribution Chart
      try {
        const hourlyCtx = document.getElementById('hourlyDistributionChart');
        if (hourlyCtx) {
          new Chart(hourlyCtx.getContext('2d'), {
            type: 'line',
            data: {
              labels: Array.from({length: 24}, (_, i) => `${i}:00`),
              datasets: [{
                label: 'Average Sales',
                data: hourlyData.hours ? hourlyData.hours.map(hour => hour.average) : [],
                borderColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return `Avg: ${formatCurrency(context.raw)}`;
                    }
                  }
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }
      } catch (error) {
        console.error('Error initializing hourly distribution chart:', error);
      }
    }); // Close DOMContentLoaded
  </script>
</body>
</html>
