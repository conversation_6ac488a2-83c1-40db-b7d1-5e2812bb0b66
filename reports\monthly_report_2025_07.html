<!DOCTYPE html>
<html>
<head>
  <title>Monthly Report - 7/2025</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .chart-container {
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 500px;
      width: 100%;
    }

    @media (max-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
    .daily-section,
    .payment-section {
      display: flex;
      gap: 30px;
      margin: 20px 0;
      align-items: flex-start;
    }
    .daily-chart,
    .payment-chart {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 600px;
    }
    .daily-table-container,
    .payment-table-container {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      overflow-x: auto;
    }
    .daily-table-container {
      overflow-x: auto; /* Allow horizontal scrolling for wide tables */
    }
    .daily-table,
    .payment-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-size: 0.9em;
    }
    .daily-table {
      font-size: 0.8em; /* Smaller font for more columns */
    }
    .daily-table th,
    .daily-table td,
    .payment-table th,
    .payment-table td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #eee;
    }
    .daily-table th,
    .payment-table th {
      background: #3498db;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }
    .daily-table th:nth-child(n+2),
    .payment-table th:nth-child(n+2) {
      text-align: right;
    }
    .daily-table tr:hover,
    .payment-table tr:hover {
      background: #f8f9fa;
    }
    .daily-table tr:last-child td,
    .payment-table tr:last-child td {
      border-bottom: none;
    }
    .payment-amount {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }
    .payment-percentage {
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-method-name {
      font-weight: 500;
      color: #34495e;
    }
    .table-total {
      background: #ecf0f1 !important;
      font-weight: 600;
      border-top: 2px solid #3498db;
    }
    .summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 1.1em;
    }
    .summary-value {
      font-weight: bold;
      color: #2c3e50;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #7f8c8d;
      font-size: 0.9em;
    }

    /* Toggle Controls */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 20px;
    }

    .section-title {
      margin: 0;
      font-size: 1.5em;
      color: #2c3e50;
    }

    .toggle-control {
      display: inline-flex;
      background: #ecf0f1;
      border-radius: 20px;
      padding: 3px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .toggle-button {
      padding: 6px 16px;
      border: none;
      background: transparent;
      border-radius: 17px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.3s ease;
      color: #7f8c8d;
    }

    .toggle-button.active {
      background: #3498db;
      color: white;
      box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    }

    .toggle-button:hover:not(.active) {
      color: #2c3e50;
    }

    /* Hide/Show functionality */
    .chart-view .table-container,
    .table-view .chart-container-section {
      display: none;
    }

    .chart-view .chart-container-section,
    .table-view .table-container {
      display: block;
      flex: 1;
    }

    /* Full width when only one view is shown */
    .section-content.chart-view .chart-container-section,
    .section-content.table-view .table-container {
      width: 100%;
      max-width: none;
    }

    /* Chart container sizing fixes */
    .chart-container-section {
      position: relative;
      height: 600px;
      max-height: 600px;
      overflow: hidden;
    }

    .chart-container-section canvas {
      max-width: 100% !important;
      max-height: 100% !important;
    }

    @media (max-width: 768px) {
      .section-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }

      .chart-container-section {
        height: 400px;
        max-height: 400px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Monthly Sales Report - 7/2025</h1>
      <p>Generated on 2025-08-17 00:25:29</p>
    </div>

    <div class="summary">
      <h2>Summary</h2>
      <div id="summary-content">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>

    <div class="daily-section">
      <div class="section-header">
        <h2 class="section-title">Daily Sales - 7/2025</h2>
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('daily', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('daily', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="daily-content">
        <div class="chart-container-section daily-chart">
          <div style="position: relative; height: calc(100% - 20px); width: 100%;">
            <canvas id="dailySalesChart"></canvas>
          </div>
        </div>

        <div class="table-container daily-table-container">
          <table class="daily-table" id="dailySalesTable">
            <thead id="dailyTableHeader">
              <!-- Will be populated by JavaScript -->
            </thead>
            <tbody id="dailyTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="payment-section">
      <div class="section-header">
        <h2 class="section-title">Payment Methods - 7/2025</h2>
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('payment', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('payment', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="payment-content">
        <div class="chart-container-section payment-chart">
          <div style="position: relative; height: calc(100% - 20px); width: 100%;">
            <canvas id="paymentMethodsChart"></canvas>
          </div>
        </div>

        <div class="table-container payment-table-container">
          <table class="payment-table" id="paymentMethodsTable">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Percentage</th>
              </tr>
            </thead>
            <tbody id="paymentTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <h2>Hourly Distribution - 7/2025</h2>
      <div style="position: relative; height: calc(100% - 40px); width: 100%;">
        <canvas id="hourlyDistributionChart"></canvas>
      </div>
    </div>

    <div class="footer">
      <p>SnP Report Generator | Generated by Asia Club Management System</p>
    </div>
  </div>

  <script>
    // Parse the JSON data passed from Pascal
    const monthlyData = {
  "year" : 2025,
  "month" : 7,
  "totalSales" : 9.8758250000000000E+004,
  "averageDaily" : 3.1857500000000000E+003,
  "busiestDay" : {
    "day" : 5,
    "amount" : 4.6056800000000003E+003
  }
};
    const paymentData = {
  "methods" : {
    "QR Code" : 6.4047250000000000E+004,
    "Cash" : 3.4408500000000000E+004,
    "CN" : 3.0250000000000000E+002
  }
};
    const dailyData = {
  "dates" : [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31"
  ],
  "amounts" : [
    3.0555700000000011E+003,
    3.4661100000000001E+003,
    3.7563200000000002E+003,
    4.4625199999999995E+003,
    4.6056800000000012E+003,
    3.1677899999999995E+003,
    3.5032599999999993E+003,
    3.5400400000000013E+003,
    3.1964099999999999E+003,
    4.5759600000000000E+003,
    3.8454500000000007E+003,
    2.7158200000000002E+003,
    3.1769400000000014E+003,
    3.5282399999999998E+003,
    2.7648099999999972E+003,
    2.9459299999999989E+003,
    3.2910200000000000E+003,
    3.0009899999999989E+003,
    2.9812199999999998E+003,
    2.5585300000000002E+003,
    3.3712400000000007E+003,
    2.7035599999999986E+003,
    2.9239100000000003E+003,
    3.0554000000000005E+003,
    2.9922199999999993E+003,
    2.3380899999999997E+003,
    2.7713099999999999E+003,
    3.2150000000000005E+003,
    2.3757100000000000E+003,
    1.9824599999999991E+003,
    2.8907400000000011E+003
  ],
  "discounts" : [
    4.2021000000000009E+002,
    3.9841999999999985E+002,
    3.7208999999999980E+002,
    4.4333000000000021E+002,
    5.7757999999999981E+002,
    3.6661999999999995E+002,
    4.1107000000000011E+002,
    4.6671999999999997E+002,
    4.1147000000000020E+002,
    5.0190999999999997E+002,
    4.4344000000000000E+002,
    3.3218000000000006E+002,
    4.1596999999999991E+002,
    3.9491000000000014E+002,
    3.3264999999999986E+002,
    2.9228000000000003E+002,
    4.3504000000000002E+002,
    3.0596000000000004E+002,
    3.8906999999999999E+002,
    2.9191999999999996E+002,
    3.7767999999999989E+002,
    3.9166999999999990E+002,
    2.3717999999999992E+002,
    4.1729000000000008E+002,
    4.0066999999999990E+002,
    2.5560999999999999E+002,
    2.9215000000000009E+002,
    2.8826999999999998E+002,
    3.1721999999999991E+002,
    2.1839000000000004E+002,
    3.3862000000000006E+002
  ],
  "cash" : [
    9.0626999999999998E+002,
    1.0628900000000001E+003,
    1.1913400000000001E+003,
    1.0176599999999997E+003,
    9.9751999999999987E+002,
    9.6597999999999990E+002,
    1.3706200000000001E+003,
    9.6463999999999999E+002,
    9.5071000000000004E+002,
    1.6747800000000002E+003,
    1.4919499999999996E+003,
    1.0228699999999998E+003,
    1.0776299999999997E+003,
    1.4139400000000003E+003,
    1.0079399999999999E+003,
    9.4367999999999984E+002,
    1.2959300000000003E+003,
    9.3892999999999995E+002,
    1.1075899999999999E+003,
    8.7264999999999998E+002,
    1.3170099999999995E+003,
    1.2931900000000007E+003,
    9.3093999999999994E+002,
    1.5537200000000003E+003,
    1.0474199999999998E+003,
    8.1863999999999999E+002,
    1.0825900000000001E+003,
    1.0345799999999999E+003,
    1.0270300000000002E+003,
    7.3481000000000017E+002,
    1.2930499999999997E+003
  ],
  "nonCash" : [
    2.1492999999999997E+003,
    2.4032200000000003E+003,
    2.5649800000000000E+003,
    3.4448600000000006E+003,
    3.6081599999999999E+003,
    2.2018100000000018E+003,
    2.1326399999999994E+003,
    2.5753999999999996E+003,
    2.2456999999999998E+003,
    2.9011800000000007E+003,
    2.3535000000000005E+003,
    1.6929500000000003E+003,
    2.0993099999999999E+003,
    2.1143000000000015E+003,
    1.7568700000000003E+003,
    2.0022499999999998E+003,
    1.9950899999999995E+003,
    2.0620600000000004E+003,
    1.8736300000000003E+003,
    1.6858800000000003E+003,
    2.0542300000000005E+003,
    1.4103700000000001E+003,
    1.9929699999999996E+003,
    1.5016799999999998E+003,
    1.9448000000000002E+003,
    1.5194499999999998E+003,
    1.6887200000000000E+003,
    2.1804199999999996E+003,
    1.3486799999999998E+003,
    1.2476500000000001E+003,
    1.5976900000000001E+003
  ],
  "paymentMethods" : [
    "QR Code",
    "Cash",
    "CN"
  ],
  "dailyBreakdown" : [
    {
      "day" : 1,
      "total" : 3.0555700000000011E+003,
      "discounts" : 4.2021000000000009E+002,
      "cash" : 9.0626999999999998E+002,
      "nonCash" : 2.1492999999999997E+003,
      "payments" : {
        "QR Code" : 2.1492999999999997E+003,
        "Cash" : 9.0626999999999998E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 2,
      "total" : 3.4661100000000001E+003,
      "discounts" : 3.9841999999999985E+002,
      "cash" : 1.0628900000000001E+003,
      "nonCash" : 2.4032200000000003E+003,
      "payments" : {
        "QR Code" : 2.4032200000000003E+003,
        "Cash" : 1.0628900000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 3,
      "total" : 3.7563200000000002E+003,
      "discounts" : 3.7208999999999980E+002,
      "cash" : 1.1913400000000001E+003,
      "nonCash" : 2.5649800000000000E+003,
      "payments" : {
        "QR Code" : 2.5302999999999997E+003,
        "Cash" : 1.1913400000000001E+003,
        "CN" : 3.4680000000000000E+001
      }
    },
    {
      "day" : 4,
      "total" : 4.4625199999999995E+003,
      "discounts" : 4.4333000000000021E+002,
      "cash" : 1.0176599999999997E+003,
      "nonCash" : 3.4448600000000006E+003,
      "payments" : {
        "QR Code" : 3.4448600000000006E+003,
        "Cash" : 1.0176599999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 5,
      "total" : 4.6056800000000012E+003,
      "discounts" : 5.7757999999999981E+002,
      "cash" : 9.9751999999999987E+002,
      "nonCash" : 3.6081599999999999E+003,
      "payments" : {
        "QR Code" : 3.6021199999999999E+003,
        "Cash" : 9.9751999999999987E+002,
        "CN" : 6.0400000000000000E+000
      }
    },
    {
      "day" : 6,
      "total" : 3.1677899999999995E+003,
      "discounts" : 3.6661999999999995E+002,
      "cash" : 9.6597999999999990E+002,
      "nonCash" : 2.2018100000000018E+003,
      "payments" : {
        "QR Code" : 2.1075100000000016E+003,
        "Cash" : 9.6597999999999990E+002,
        "CN" : 9.4299999999999997E+001
      }
    },
    {
      "day" : 7,
      "total" : 3.5032599999999993E+003,
      "discounts" : 4.1107000000000011E+002,
      "cash" : 1.3706200000000001E+003,
      "nonCash" : 2.1326399999999994E+003,
      "payments" : {
        "QR Code" : 2.1326399999999994E+003,
        "Cash" : 1.3706200000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 8,
      "total" : 3.5400400000000013E+003,
      "discounts" : 4.6671999999999997E+002,
      "cash" : 9.6463999999999999E+002,
      "nonCash" : 2.5753999999999996E+003,
      "payments" : {
        "QR Code" : 2.5753999999999996E+003,
        "Cash" : 9.6463999999999999E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 9,
      "total" : 3.1964099999999999E+003,
      "discounts" : 4.1147000000000020E+002,
      "cash" : 9.5071000000000004E+002,
      "nonCash" : 2.2456999999999998E+003,
      "payments" : {
        "QR Code" : 2.2331499999999996E+003,
        "Cash" : 9.5071000000000004E+002,
        "CN" : 1.2550000000000001E+001
      }
    },
    {
      "day" : 10,
      "total" : 4.5759600000000000E+003,
      "discounts" : 5.0190999999999997E+002,
      "cash" : 1.6747800000000002E+003,
      "nonCash" : 2.9011800000000007E+003,
      "payments" : {
        "QR Code" : 2.8769400000000005E+003,
        "Cash" : 1.6747800000000002E+003,
        "CN" : 2.4239999999999998E+001
      }
    },
    {
      "day" : 11,
      "total" : 3.8454500000000007E+003,
      "discounts" : 4.4344000000000000E+002,
      "cash" : 1.4919499999999996E+003,
      "nonCash" : 2.3535000000000005E+003,
      "payments" : {
        "QR Code" : 2.3535000000000005E+003,
        "Cash" : 1.4919499999999996E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 12,
      "total" : 2.7158200000000002E+003,
      "discounts" : 3.3218000000000006E+002,
      "cash" : 1.0228699999999998E+003,
      "nonCash" : 1.6929500000000003E+003,
      "payments" : {
        "QR Code" : 1.6929500000000003E+003,
        "Cash" : 1.0228699999999998E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 13,
      "total" : 3.1769400000000014E+003,
      "discounts" : 4.1596999999999991E+002,
      "cash" : 1.0776299999999997E+003,
      "nonCash" : 2.0993099999999999E+003,
      "payments" : {
        "QR Code" : 2.0993099999999999E+003,
        "Cash" : 1.0776299999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 14,
      "total" : 3.5282399999999998E+003,
      "discounts" : 3.9491000000000014E+002,
      "cash" : 1.4139400000000003E+003,
      "nonCash" : 2.1143000000000015E+003,
      "payments" : {
        "QR Code" : 2.1143000000000015E+003,
        "Cash" : 1.4139400000000003E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 15,
      "total" : 2.7648099999999972E+003,
      "discounts" : 3.3264999999999986E+002,
      "cash" : 1.0079399999999999E+003,
      "nonCash" : 1.7568700000000003E+003,
      "payments" : {
        "QR Code" : 1.7568700000000003E+003,
        "Cash" : 1.0079399999999999E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 16,
      "total" : 2.9459299999999989E+003,
      "discounts" : 2.9228000000000003E+002,
      "cash" : 9.4367999999999984E+002,
      "nonCash" : 2.0022499999999998E+003,
      "payments" : {
        "QR Code" : 1.9898299999999999E+003,
        "Cash" : 9.4367999999999984E+002,
        "CN" : 1.2420000000000000E+001
      }
    },
    {
      "day" : 17,
      "total" : 3.2910200000000000E+003,
      "discounts" : 4.3504000000000002E+002,
      "cash" : 1.2959300000000003E+003,
      "nonCash" : 1.9950899999999995E+003,
      "payments" : {
        "QR Code" : 1.9950899999999995E+003,
        "Cash" : 1.2959300000000003E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 18,
      "total" : 3.0009899999999989E+003,
      "discounts" : 3.0596000000000004E+002,
      "cash" : 9.3892999999999995E+002,
      "nonCash" : 2.0620600000000004E+003,
      "payments" : {
        "QR Code" : 2.0146600000000001E+003,
        "Cash" : 9.3892999999999995E+002,
        "CN" : 4.7399999999999999E+001
      }
    },
    {
      "day" : 19,
      "total" : 2.9812199999999998E+003,
      "discounts" : 3.8906999999999999E+002,
      "cash" : 1.1075899999999999E+003,
      "nonCash" : 1.8736300000000003E+003,
      "payments" : {
        "QR Code" : 1.8736300000000003E+003,
        "Cash" : 1.1075899999999999E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 20,
      "total" : 2.5585300000000002E+003,
      "discounts" : 2.9191999999999996E+002,
      "cash" : 8.7264999999999998E+002,
      "nonCash" : 1.6858800000000003E+003,
      "payments" : {
        "QR Code" : 1.6858800000000003E+003,
        "Cash" : 8.7264999999999998E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 21,
      "total" : 3.3712400000000007E+003,
      "discounts" : 3.7767999999999989E+002,
      "cash" : 1.3170099999999995E+003,
      "nonCash" : 2.0542300000000005E+003,
      "payments" : {
        "QR Code" : 2.0434100000000005E+003,
        "Cash" : 1.3170099999999995E+003,
        "CN" : 1.0820000000000000E+001
      }
    },
    {
      "day" : 22,
      "total" : 2.7035599999999986E+003,
      "discounts" : 3.9166999999999990E+002,
      "cash" : 1.2931900000000007E+003,
      "nonCash" : 1.4103700000000001E+003,
      "payments" : {
        "QR Code" : 1.4103700000000001E+003,
        "Cash" : 1.2931900000000007E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 23,
      "total" : 2.9239100000000003E+003,
      "discounts" : 2.3717999999999992E+002,
      "cash" : 9.3093999999999994E+002,
      "nonCash" : 1.9929699999999996E+003,
      "payments" : {
        "QR Code" : 1.9929699999999996E+003,
        "Cash" : 9.3093999999999994E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 24,
      "total" : 3.0554000000000005E+003,
      "discounts" : 4.1729000000000008E+002,
      "cash" : 1.5537200000000003E+003,
      "nonCash" : 1.5016799999999998E+003,
      "payments" : {
        "QR Code" : 1.5016799999999998E+003,
        "Cash" : 1.5537200000000003E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 25,
      "total" : 2.9922199999999993E+003,
      "discounts" : 4.0066999999999990E+002,
      "cash" : 1.0474199999999998E+003,
      "nonCash" : 1.9448000000000002E+003,
      "payments" : {
        "QR Code" : 1.9448000000000002E+003,
        "Cash" : 1.0474199999999998E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 26,
      "total" : 2.3380899999999997E+003,
      "discounts" : 2.5560999999999999E+002,
      "cash" : 8.1863999999999999E+002,
      "nonCash" : 1.5194499999999998E+003,
      "payments" : {
        "QR Code" : 1.5194499999999998E+003,
        "Cash" : 8.1863999999999999E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 27,
      "total" : 2.7713099999999999E+003,
      "discounts" : 2.9215000000000009E+002,
      "cash" : 1.0825900000000001E+003,
      "nonCash" : 1.6887200000000000E+003,
      "payments" : {
        "QR Code" : 1.6887200000000000E+003,
        "Cash" : 1.0825900000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 28,
      "total" : 3.2150000000000005E+003,
      "discounts" : 2.8826999999999998E+002,
      "cash" : 1.0345799999999999E+003,
      "nonCash" : 2.1804199999999996E+003,
      "payments" : {
        "QR Code" : 2.1650199999999995E+003,
        "Cash" : 1.0345799999999999E+003,
        "CN" : 1.5400000000000000E+001
      }
    },
    {
      "day" : 29,
      "total" : 2.3757100000000000E+003,
      "discounts" : 3.1721999999999991E+002,
      "cash" : 1.0270300000000002E+003,
      "nonCash" : 1.3486799999999998E+003,
      "payments" : {
        "QR Code" : 1.3129399999999998E+003,
        "Cash" : 1.0270300000000002E+003,
        "CN" : 3.5740000000000002E+001
      }
    },
    {
      "day" : 30,
      "total" : 1.9824599999999991E+003,
      "discounts" : 2.1839000000000004E+002,
      "cash" : 7.3481000000000017E+002,
      "nonCash" : 1.2476500000000001E+003,
      "payments" : {
        "QR Code" : 1.2476500000000001E+003,
        "Cash" : 7.3481000000000017E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 31,
      "total" : 2.8907400000000011E+003,
      "discounts" : 3.3862000000000006E+002,
      "cash" : 1.2930499999999997E+003,
      "nonCash" : 1.5976900000000001E+003,
      "payments" : {
        "QR Code" : 1.5887800000000002E+003,
        "Cash" : 1.2930499999999997E+003,
        "CN" : 8.9100000000000001E+000
      }
    }
  ]
};
    const hourlyData = {
  "hours" : [
    {
      "hour" : 0,
      "total" : 7.5535900000000020E+003,
      "count" : 326,
      "average" : 2.3170521472392643E+001
    },
    {
      "hour" : 1,
      "total" : 5.0324100000000044E+003,
      "count" : 253,
      "average" : 1.9890948616600809E+001
    },
    {
      "hour" : 2,
      "total" : 2.6967100000000005E+003,
      "count" : 149,
      "average" : 1.8098724832214767E+001
    },
    {
      "hour" : 3,
      "total" : 6.7652999999999997E+002,
      "count" : 53,
      "average" : 1.2764716981132075E+001
    },
    {
      "hour" : 4,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 5,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 6,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 7,
      "total" : 9.4999999999999996E-001,
      "count" : 1,
      "average" : 9.4999999999999996E-001
    },
    {
      "hour" : 8,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 9,
      "total" : 2.5048000000000002E+002,
      "count" : 11,
      "average" : 2.2770909090909093E+001
    },
    {
      "hour" : 10,
      "total" : 1.4889800000000000E+003,
      "count" : 66,
      "average" : 2.2560303030303032E+001
    },
    {
      "hour" : 11,
      "total" : 2.7787199999999993E+003,
      "count" : 101,
      "average" : 2.7512079207920785E+001
    },
    {
      "hour" : 12,
      "total" : 3.8265099999999998E+003,
      "count" : 163,
      "average" : 2.3475521472392636E+001
    },
    {
      "hour" : 13,
      "total" : 3.9541200000000008E+003,
      "count" : 193,
      "average" : 2.0487668393782389E+001
    },
    {
      "hour" : 14,
      "total" : 5.2549399999999987E+003,
      "count" : 226,
      "average" : 2.3251946902654861E+001
    },
    {
      "hour" : 15,
      "total" : 4.9258400000000065E+003,
      "count" : 266,
      "average" : 1.8518195488721830E+001
    },
    {
      "hour" : 16,
      "total" : 7.3868800000000056E+003,
      "count" : 350,
      "average" : 2.1105371428571445E+001
    },
    {
      "hour" : 17,
      "total" : 7.0556200000000044E+003,
      "count" : 350,
      "average" : 2.0158914285714300E+001
    },
    {
      "hour" : 18,
      "total" : 6.1978399999999956E+003,
      "count" : 261,
      "average" : 2.3746513409961668E+001
    },
    {
      "hour" : 19,
      "total" : 5.9708699999999990E+003,
      "count" : 204,
      "average" : 2.9268970588235288E+001
    },
    {
      "hour" : 20,
      "total" : 6.2347000000000025E+003,
      "count" : 215,
      "average" : 2.8998604651162804E+001
    },
    {
      "hour" : 21,
      "total" : 7.8852499999999982E+003,
      "count" : 273,
      "average" : 2.8883699633699628E+001
    },
    {
      "hour" : 22,
      "total" : 1.0612489999999998E+004,
      "count" : 373,
      "average" : 2.8451715817694364E+001
    },
    {
      "hour" : 23,
      "total" : 8.9748200000000070E+003,
      "count" : 361,
      "average" : 2.4860997229916919E+001
    }
  ]
};

    // Toggle functionality
    function toggleView(section, view) {
      const contentElement = document.getElementById(section + '-content');
      const buttons = contentElement.parentElement.querySelectorAll('.toggle-button');

      // Update button states
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      // Update content view
      contentElement.className = 'section-content ' + view + '-view';

      // Save preference
      localStorage.setItem(section + '-view', view);
    }

    // Load saved preferences
    function loadViewPreferences() {
      const dailyView = localStorage.getItem('daily-view') || 'table';
      const paymentView = localStorage.getItem('payment-view') || 'table';

      // Set daily section
      const dailyContent = document.getElementById('daily-content');
      const dailyButtons = dailyContent.parentElement.querySelectorAll('.toggle-button');
      dailyContent.className = 'section-content ' + dailyView + '-view';
      dailyButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(dailyView));
      });

      // Set payment section
      const paymentContent = document.getElementById('payment-content');
      const paymentButtons = paymentContent.parentElement.querySelectorAll('.toggle-button');
      paymentContent.className = 'section-content ' + paymentView + '-view';
      paymentButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(paymentView));
      });
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2
      }).format(amount);
    }
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Load view preferences first
      loadViewPreferences();

      try {
        // Daily Sales Chart
        const dailyCtx = document.getElementById('dailySalesChart');
        if (dailyCtx) {
          // Prepare data for multiple series
          const datasets = [];

          // Check if we have breakdown data
          if (dailyData.cash && dailyData.nonCash && dailyData.discounts) {
            // Show 4 series: Total, Cash, Non-Cash, Discounts
            datasets.push({
              label: 'Total Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Cash',
              data: dailyData.cash || [],
              backgroundColor: 'rgba(75, 192, 192, 0.8)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Non-Cash',
              data: dailyData.nonCash || [],
              backgroundColor: 'rgba(255, 159, 64, 0.8)',
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Discounts',
              data: dailyData.discounts || [],
              backgroundColor: 'rgba(255, 99, 132, 0.8)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2
            });
          } else {
            // Fallback to single series
            datasets.push({
              label: 'Daily Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
          }

          new Chart(dailyCtx, {
            type: 'bar',
            data: {
              labels: dailyData.dates || [],
              datasets: datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return formatCurrency(context.raw);
                    }
                  }
                },
                legend: {
                  display: datasets.length > 1
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Daily Sales Table
        const dailyTableBody = document.getElementById('dailyTableBody');
        const dailyTableHeader = document.getElementById('dailyTableHeader');
        if (dailyTableBody && dailyTableHeader && dailyData.dates && dailyData.amounts) {
          const paymentMethods = dailyData.paymentMethods || [];
          const dailyBreakdown = dailyData.dailyBreakdown || [];

          // Clear existing content
          dailyTableBody.innerHTML = '';
          dailyTableHeader.innerHTML = '';

          // Create header row
          const headerRow = document.createElement('tr');
          headerRow.innerHTML = `
            <th>Day</th>
            <th>Total Sales</th>
            <th>Discounts</th>
            <th>% of Month</th>
            ${paymentMethods.map(method => `<th>${method}</th>`).join('')}
          `;
          dailyTableHeader.appendChild(headerRow);

          const monthTotal = dailyData.amounts.reduce((sum, amount) => sum + amount, 0);

          // Add rows for each day
          dailyData.dates.forEach((date, index) => {
            const amount = dailyData.amounts[index] || 0;
            const discount = (dailyData.discounts && dailyData.discounts[index]) || 0;
            const percentage = monthTotal > 0 ? (amount / monthTotal * 100).toFixed(1) : '0.0';
            const dayNumber = index + 1; // Convert to 1-based day

            // Find payment breakdown for this day
            const dayBreakdown = dailyBreakdown.find(db => db.day === dayNumber);
            const payments = dayBreakdown ? dayBreakdown.payments : {};

            const displayAmount = amount > 0 ? formatCurrency(amount) : '';
            const displayDiscount = discount > 0 ? formatCurrency(discount) : '';
            const displayPercentage = amount > 0 ? percentage + '%' : '';

            // Generate payment method cells
            let paymentCells = '';
            paymentMethods.forEach(method => {
              const methodAmount = payments[method] || 0;
              const displayMethodAmount = methodAmount > 0 ? formatCurrency(methodAmount) : '';
              paymentCells += `<td class="payment-amount">${displayMethodAmount}</td>`;
            });

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${date}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-amount">${displayDiscount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
              ${paymentCells}
            `;
            dailyTableBody.appendChild(row);
          });

          // Add total row
          if (monthTotal > 0) {
            // Calculate totals for each column
            const totalDiscounts = (dailyData.discounts || []).reduce((sum, amount) => sum + amount, 0);

            // Calculate payment method totals
            let paymentTotalCells = '';
            paymentMethods.forEach(method => {
              let methodTotal = 0;
              dailyBreakdown.forEach(db => {
                methodTotal += db.payments[method] || 0;
              });
              paymentTotalCells += `<td class="payment-amount"><strong>${formatCurrency(methodTotal)}</strong></td>`;
            });

            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(monthTotal)}</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalDiscounts)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
              ${paymentTotalCells}
            `;
            dailyTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing daily sales chart:', error);
      }

      // Payment Methods Chart
      try {
        const paymentCtx = document.getElementById('paymentMethodsChart');
        if (paymentCtx) {
          new Chart(paymentCtx, {
            type: 'pie',
            data: {
              labels: Object.keys(paymentData.methods || {}),
              datasets: [{
                data: Object.values(paymentData.methods || {}),
                backgroundColor: [
                  '#3498db', '#2ecc71', '#e74c3c', '#f1c40f',
                  '#9b59b6', '#1abc9c', '#e67e22', '#74c0fc'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Payment Methods Distribution',
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = Math.round((value / total) * 100);
                      return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Payment Methods Table
        const paymentTableBody = document.getElementById('paymentTableBody');
        if (paymentTableBody && paymentData.methods) {
          const methods = paymentData.methods;
          const methodNames = Object.keys(methods);
          const methodAmounts = Object.values(methods);

          // Calculate total for percentages
          const totalAmount = methodAmounts.reduce((sum, amount) => sum + amount, 0);

          // Sort methods by amount (descending)
          const sortedMethods = methodNames
            .map(name => ({ name, amount: methods[name] }))
            .sort((a, b) => b.amount - a.amount);

          // Clear existing content
          paymentTableBody.innerHTML = '';

          // Add rows for each payment method
          sortedMethods.forEach(method => {
            const percentage = totalAmount > 0 ? (method.amount / totalAmount * 100).toFixed(1) : '0.0';
            const displayAmount = method.amount > 0 ? formatCurrency(method.amount) : '';
            const displayPercentage = method.amount > 0 ? percentage + '%' : '';

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${method.name}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
            `;
            paymentTableBody.appendChild(row);
          });

          // Add total row
          if (totalAmount > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalAmount)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
            `;
            paymentTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing payment methods chart:', error);
      }

      // Update summary content
      try {
        const summaryContent = document.getElementById('summary-content');
        if (summaryContent && monthlyData) {
          summaryContent.innerHTML =
            '<div class="summary-item">' +
            'Total Sales: <span class="summary-value">' + formatCurrency(monthlyData.totalSales || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Average Daily Sales: <span class="summary-value">' + formatCurrency(monthlyData.averageDaily || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Busiest Day: <span class="summary-value">' +
            (monthlyData.busiestDay ? monthlyData.busiestDay.day + ' (' + formatCurrency(monthlyData.busiestDay.amount) + ')' : 'N/A') +
            '</span></div>';
        }
      } catch (error) {
        console.error('Error updating summary content:', error);
      }

      // Hourly Distribution Chart
      try {
        const hourlyCtx = document.getElementById('hourlyDistributionChart');
        if (hourlyCtx) {
          new Chart(hourlyCtx.getContext('2d'), {
            type: 'line',
            data: {
              labels: Array.from({length: 24}, (_, i) => `${i}:00`),
              datasets: [{
                label: 'Average Sales',
                data: hourlyData.hours ? hourlyData.hours.map(hour => hour.average) : [],
                borderColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return `Avg: ${formatCurrency(context.raw)}`;
                    }
                  }
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }
      } catch (error) {
        console.error('Error initializing hourly distribution chart:', error);
      }
    }); // Close DOMContentLoaded
  </script>
</body>
</html>
