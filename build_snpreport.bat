@echo off
echo Building SNP Report Generator...

REM Set the path to your Free Pascal Compiler (FPC)
set FPC_PATH=D:\lazarus4\fpc\3.2.2\bin\x86_64-win64\fpc.exe

REM Set the output directory
set OUTPUT_DIR=bin

REM Create output directory if it doesn't exist
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Compile the application
"%FPC_PATH%" ^
  -FE%OUTPUT_DIR% ^
  -Fu. ^
  -FuD:\lazarus4\fpc\3.2.2\units\x86_64-win64\rtl ^
  -FuD:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-base ^
  -FuD:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-json ^
  -FuD:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-xml ^
  -FuD:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-process ^
  -FuD:\lazarus4\fpc\3.2.2\units\x86_64-win64\ibase ^
  -FuD:\lazarus4\components\lazutils\lib\x86_64-win64 ^
  -FuD:\lazarus4\lcl\units\x86_64-win64 ^
  -FuD:\lazarus4\lcl\units\x86_64-win64\lclbase ^
  -FuD:\lazarus4\components\lazcontrols\lib\x86_64-win64\lcl ^
  -FuC:\Users\<USER>\AppData\Local\lazarus4\onlinepackagemanager\packages\synapse40.1 ^
  -dLCL ^
  -MObjFPC -Scghi -Cg -g -gl -l -vewnhibq ^
  -Fi. ^
  snpreport.lpr

REM Check if compilation was successful
if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo Compilation successful!
echo.

REM Run the application with example parameters
echo Running SNP Report Generator...
echo.
cd %OUTPUT_DIR%  
snpreport.exe --year 2025 --month 8

REM Keep the console window open to see the output
pause
