unit uDB;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, SQLDB, IBConnection, IniFiles, Windows, ShlObj, DateUtils;

type
  PSessionData = ^TSessionData;
  TSessionData = record
    Id: Integer;
    StartTime: TDateTime;
    EndTime: TDateTime;
    TotalAmount: Double;
    MemberDiscount: Double;
    OperatorDiscount: Double;
    FinalAmount: Double;
    PaymentMethod: string;
    PlayerId: Integer;
  end;
  TSessionArray = array of TSessionData;

  TSnpDatabase = class
  private
    FConnection: TIBConnection;
    FTransaction: TSQLTransaction;
    FQuery: TSQLQuery;
    FSessionList: TList;
    function GetDatabasePath: string;
  public
    constructor Create;
    destructor Destroy; override;
    procedure Connect;
    function GetSessionsByYear(Year: Integer): TSessionArray;
    function GetSessionsByMonth(Year, Month: Integer): TSessionArray;
    function GetPaymentMethods: TStringList;
  end;

var
   Session: TSessionData;
   SessionList: TList;

implementation

{ TSnpDatabase }

constructor TSnpDatabase.Create;
begin
  FConnection := TIBConnection.Create(nil);
  FTransaction := TSQLTransaction.Create(nil);
  FQuery := TSQLQuery.Create(nil);
  FSessionList := TList.Create;
  
  FConnection.Transaction := FTransaction;
  FQuery.DataBase := FConnection;
end;

destructor TSnpDatabase.Destroy;
var
  i: Integer;
  Session: PSessionData;
begin
  // Clean up any remaining sessions in the list
  if Assigned(FSessionList) then
  begin
    for i := 0 to FSessionList.Count - 1 do
    begin
      Session := PSessionData(FSessionList[i]);
      if Assigned(Session) then
        Dispose(Session);
    end;
    FSessionList.Free;
  end;
  
  FQuery.Free;
  if Assigned(FConnection) and FConnection.Connected then
    FConnection.Connected := False;
  FTransaction.Free;
  FConnection.Free;
  inherited Destroy;
end;

function TSnpDatabase.GetDatabasePath: string;
var
  IniFile: TIniFile;
  NetworkPath: string;
  Path: array[0..MAX_PATH] of Char;
begin
  // First try to get network path from config.ini
  if FileExists(ExtractFilePath(ParamStr(0)) + 'snpconfig.ini') then
  begin
    with TIniFile.Create(ExtractFilePath(ParamStr(0)) + 'snpconfig.ini') do
    try
      NetworkPath := ReadString('Database', 'NetworkPath', '');
      if NetworkPath <> '' then
      begin
        Result := IncludeTrailingPathDelimiter(NetworkPath) + 'snpdb.fdb';
        Exit;
      end;
    finally
      Free;
    end;
  end;

  // Try LocalAppData next
  if SHGetFolderPath(0, CSIDL_LOCAL_APPDATA, 0, 0, @Path) = S_OK then
  begin
    Result := IncludeTrailingPathDelimiter(Path) + 'SNP\data\snpdb.fdb';
    if FileExists(Result) then Exit;
  end;
  
  // Try CommonAppData next
  if SHGetFolderPath(0, CSIDL_COMMON_APPDATA, 0, 0, @Path) = S_OK then
  begin
    Result := IncludeTrailingPathDelimiter(Path) + 'SNP\data\snpdb.fdb';
    if FileExists(Result) then Exit;
  end;
  
  // Fall back to application directory
  Result := ExtractFilePath(ParamStr(0)) + 'data\snpdb.fdb';
end;

procedure TSnpDatabase.Connect;
var
  DBPath: string;
begin
  try
    DBPath := GetDatabasePath;
    WriteLn('Attempting to connect to database: ', DBPath);
    
    if not FileExists(DBPath) then
      raise Exception.CreateFmt('Database file not found: %s', [DBPath]);
      
    FConnection.DatabaseName := DBPath;
    FConnection.UserName := 'SYSDBA';
    FConnection.Password := 'masterkey'; // In production, this should be secured
    
    // Set connection parameters
    FConnection.CharSet := 'UTF8';
    //FConnection.ClientCodepage := 'UTF8';
    
    // Extract host from database path if it exists
    if Pos(':', FConnection.DatabaseName) > 0 then
      FConnection.HostName := Copy(FConnection.DatabaseName, 1, Pos(':', FConnection.DatabaseName) - 1)
    else
      FConnection.HostName := 'localhost';
      
    WriteLn('Connecting to Firebird server: ', FConnection.HostName);
    FConnection.Connected := True;
    
    // Test the connection with a simple query
    FQuery.SQL.Text := 'SELECT 1 FROM RDB$DATABASE';
    FQuery.Open;
    try
      if not FQuery.EOF then
        WriteLn('Successfully connected to database');
    finally
      FQuery.Close;
    end;
    
  except
    on E: Exception do
    begin
      WriteLn('Error connecting to database: ', E.Message);
      raise; // Re-raise the exception for the caller to handle
    end;
  end;
end;

function TSnpDatabase.GetSessionsByYear(Year: Integer): TSessionArray;
var
  StartDate, EndDate: TDateTime;
  Session: PSessionData;
  i: Integer;
begin
  StartDate := EncodeDate(Year, 1, 1);
  EndDate := IncYear(StartDate, 1);
  
  // Clear any existing sessions
  for i := 0 to FSessionList.Count - 1 do
  begin
    Session := PSessionData(FSessionList[i]);
    if Assigned(Session) then
      Dispose(Session);
  end;
  FSessionList.Clear;
  
  // Get the session data
  FQuery.SQL.Text :=
    'SELECT ID, START_TIME, END_TIME, TOTAL_AMOUNT, MEMBER_DISCOUNT, ' +
    'OPERATOR_DISCOUNT, FINAL_AMOUNT, PAYMENT_METHOD, PLAYER_ID ' +
    'FROM SESSIONS ' +
    'WHERE START_TIME >= :StartDate AND START_TIME < :EndDate ' +
    'ORDER BY START_TIME';
    
  FQuery.Params.ParamByName('StartDate').AsDateTime := StartDate;
  FQuery.Params.ParamByName('EndDate').AsDateTime := EndDate;
  
  FQuery.Open;
  try
    while not FQuery.EOF do
    begin
      // Allocate memory for a new session record
      New(Session);
      try
        // Fill the record
        Session^.Id := FQuery.FieldByName('ID').AsInteger;
        Session^.StartTime := FQuery.FieldByName('START_TIME').AsDateTime;
        Session^.EndTime := FQuery.FieldByName('END_TIME').AsDateTime;
        Session^.TotalAmount := FQuery.FieldByName('TOTAL_AMOUNT').AsFloat;
        Session^.MemberDiscount := FQuery.FieldByName('MEMBER_DISCOUNT').AsFloat;
        Session^.OperatorDiscount := FQuery.FieldByName('OPERATOR_DISCOUNT').AsFloat;
        Session^.FinalAmount := FQuery.FieldByName('FINAL_AMOUNT').AsFloat;
        Session^.PaymentMethod := FQuery.FieldByName('PAYMENT_METHOD').AsString;
        Session^.PlayerId := FQuery.FieldByName('PLAYER_ID').AsInteger;
        
        // Add the pointer to the list
        FSessionList.Add(Session);
        Session := nil; // Prevent double-free in case of exception
      except
        if Assigned(Session) then
          Dispose(Session);
        raise;
      end;
      
      FQuery.Next;
    end;
  finally
    FQuery.Close;
  end;
  
  // Convert the TList to TSessionArray
  SetLength(Result, FSessionList.Count);
  for i := 0 to FSessionList.Count - 1 do
  begin
    Session := PSessionData(FSessionList[i]);
    Result[i] := Session^;
  end;
end;

function TSnpDatabase.GetSessionsByMonth(Year, Month: Integer): TSessionArray;
var
  StartDate, EndDate: TDateTime;
  Session: PSessionData;
  i: Integer;
begin
  StartDate := EncodeDate(Year, Month, 1);
  EndDate := IncMonth(StartDate, 1);
  
  // Clear any existing sessions
  for i := 0 to FSessionList.Count - 1 do
  begin
    Session := PSessionData(FSessionList[i]);
    if Assigned(Session) then
      Dispose(Session);
  end;
  FSessionList.Clear;
  
  // Get the session data
  FQuery.SQL.Text :=
    'SELECT ID, START_TIME, END_TIME, TOTAL_AMOUNT, MEMBER_DISCOUNT, ' +
    'OPERATOR_DISCOUNT, FINAL_AMOUNT, PAYMENT_METHOD, PLAYER_ID ' +
    'FROM SESSIONS ' +
    'WHERE START_TIME >= :StartDate AND START_TIME < :EndDate ' +
    'ORDER BY START_TIME';
    
  FQuery.Params.ParamByName('StartDate').AsDateTime := StartDate;
  FQuery.Params.ParamByName('EndDate').AsDateTime := EndDate;
  
  FQuery.Open;
  try
    while not FQuery.EOF do
    begin
      // Allocate memory for a new session record
      New(Session);
      try
        // Fill the record
        Session^.Id := FQuery.FieldByName('ID').AsInteger;
        Session^.StartTime := FQuery.FieldByName('START_TIME').AsDateTime;
        Session^.EndTime := FQuery.FieldByName('END_TIME').AsDateTime;
        Session^.TotalAmount := FQuery.FieldByName('TOTAL_AMOUNT').AsFloat;
        Session^.MemberDiscount := FQuery.FieldByName('MEMBER_DISCOUNT').AsFloat;
        Session^.OperatorDiscount := FQuery.FieldByName('OPERATOR_DISCOUNT').AsFloat;
        Session^.FinalAmount := FQuery.FieldByName('FINAL_AMOUNT').AsFloat;
        Session^.PaymentMethod := FQuery.FieldByName('PAYMENT_METHOD').AsString;
        Session^.PlayerId := FQuery.FieldByName('PLAYER_ID').AsInteger;
        
        // Add the pointer to the list
        FSessionList.Add(Session);
        Session := nil; // Prevent double-free in case of exception
      except
        if Assigned(Session) then
          Dispose(Session);
        raise;
      end;
      FQuery.Next;
    end;
  finally
    FQuery.Close;
  end;
  
  // Convert the TList to TSessionArray
  SetLength(Result, FSessionList.Count);
  for i := 0 to FSessionList.Count - 1 do
  begin
    Session := PSessionData(FSessionList[i]);
    Result[i] := Session^;
  end;
end;

function TSnpDatabase.GetPaymentMethods: TStringList;
begin
  Result := TStringList.Create;
  try
    FQuery.SQL.Text := 'SELECT DISTINCT PAYMENT_METHOD FROM SESSIONS WHERE PAYMENT_METHOD IS NOT NULL';
    FQuery.Open;
    try
      while not FQuery.EOF do
      begin
        Result.Add(FQuery.FieldByName('PAYMENT_METHOD').AsString);
        FQuery.Next;
      end;
    finally
      FQuery.Close;
    end;
  except
    Result.Free;
    raise;
  end;
end;

end.
