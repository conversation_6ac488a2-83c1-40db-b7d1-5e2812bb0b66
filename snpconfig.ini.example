[Database]
; Path to your Firebird database file
; Use either NetworkPath for remote databases or LocalPath for local files
; NetworkPath=localhost:C:\path\to\SESSIONS.FDB
LocalPath=C:\path\to\SESSIONS.FDB

; Database credentials
Username=SYSDBA
Password=masterkey

; Optional: Character set (default: UTF8)
; CharacterSet=UTF8

; Optional: Role (default: empty)
; Role=

; Optional: Client library (auto-detected if not specified)
; ClientLib=gds32.dll

[Reports]
; Default output directory (relative to executable or absolute path)
OutputDir=reports

; Default currency symbol
CurrencySymbol=$

; Date format for reports (uses FreePascal format specifiers)
; See: https://www.freepascal.org/docs-html/rtl/sysutils/formatchars.html
DateFormat=yyyy-mm-dd

[Charts]
; Chart.js version (from CDN)
ChartJSVersion=3.9.1

; Chart colors
Color1=rgba(75, 192, 192, 0.6)
Color2=rgba(54, 162, 235, 0.6)
Color3=rgba(255, 99, 132, 0.6)
Color4=rgba(255, 159, 64, 0.6)
Color5=rgba(153, 102, 255, 0.6)
