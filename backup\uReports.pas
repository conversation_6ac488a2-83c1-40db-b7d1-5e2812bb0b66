unit uReports;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, uDB, DateUtils, StrUtils, Contnrs;

type
  TSnpReport = class
  private
    FDatabase: TSnpDatabase;
    FTemplatePath: string;
    function LoadTemplate(const TemplateName: string): string;
    function GenerateYearlyData(Year: Integer): string;
    function GenerateMonthlyData(Year, Month: Integer): string;
    function GeneratePaymentMethodsData(Year, Month: Integer = 0): string;
    function GenerateDailySalesData(Year, Month: Integer): string;
    function GenerateHourlyData(Year, Month: Integer): string;
    function EscapeJSON(const Str: string): string;
    function FormatCurrency(Amount: Double): string;
  public
    constructor Create(Database: TSnpDatabase);
    procedure GenerateYearlyReport(Year: Integer; const OutputDir: string);
    procedure GenerateMonthlyReport(Year, Month: Integer; const OutputDir: string);
  end;

implementation

{ TSnpReport }

constructor TSnpReport.Create(Database: TSnpDatabase);
begin
  inherited Create;
  FDatabase := Database;
  FTemplatePath := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0)) + 'templates');
  if not DirectoryExists(FTemplatePath) then
    CreateDir(FTemplatePath);
end;

function TSnpReport.LoadTemplate(const TemplateName: string): string;
var
  TemplateFile: TStringList;
  TemplatePath: string;
begin
  TemplatePath := IncludeTrailingPathDelimiter(FTemplatePath) + TemplateName;
  if not FileExists(TemplatePath) then
    raise Exception.CreateFmt('Template not found: %s', [TemplatePath]);
    
  TemplateFile := TStringList.Create;
  try
    TemplateFile.LoadFromFile(TemplatePath);
    Result := TemplateFile.Text;
  finally
    TemplateFile.Free;
  end;
end;

function TSnpReport.EscapeJSON(const Str: string): string;
begin
  Result := StringReplace(Str, '\', '\\', [rfReplaceAll]);
  Result := StringReplace(Result, '"', '\"', [rfReplaceAll]);
  Result := StringReplace(Result, #13#10, '\n', [rfReplaceAll]);
  Result := StringReplace(Result, #10, '\n', [rfReplaceAll]);
  Result := StringReplace(Result, #13, '\n', [rfReplaceAll]);
end;

function TSnpReport.FormatCurrency(Amount: Double): string;
begin
  Result := FormatFloat('0.00', Amount);
end;

function TSnpReport.GenerateYearlyData(Year: Integer): string;
var
  Sessions: TSessionArray;
  MonthlyTotals: array[1..12] of Double;
  PaymentTotals: TStringList;
  i, j: Integer;
  Month, Day: Word;
  Total, MonthlyAvg, YearlyTotal: Double;
  BestMonth: Integer;
  BestAmount: Double;
  ResultJSON: TStringList;
begin
  // Initialize
  Sessions := FDatabase.GetSessionsByYear(Year);
  FillChar(MonthlyTotals, SizeOf(MonthlyTotals), 0);
  PaymentTotals := TStringList.Create;
  try
    // Calculate monthly totals
    for i := 0 to High(Sessions) do
    begin
      DecodeDate(Sessions[i].StartTime, Year, Month, Day);
      MonthlyTotals[Month] := MonthlyTotals[Month] + Sessions[i].FinalAmount;
      
      // Track payment methods
      if PaymentTotals.IndexOf(Sessions[i].PaymentMethod) = -1 then
        PaymentTotals.AddObject(Sessions[i].PaymentMethod, TObject(0));
      
      j := PaymentTotals.IndexOf(Sessions[i].PaymentMethod);
      PaymentTotals.Objects[j] := TObject(Integer(PaymentTotals.Objects[j]) + 
        Trunc(Sessions[i].FinalAmount * 100));
    end;
    
    // Calculate yearly total and find best month
    YearlyTotal := 0;
    BestAmount := 0;
    BestMonth := 1;
    for i := 1 to 12 do
    begin
      YearlyTotal := YearlyTotal + MonthlyTotals[i];
      if MonthlyTotals[i] > BestAmount then
      begin
        BestAmount := MonthlyTotals[i];
        BestMonth := i;
      end;
    end;
    MonthlyAvg := YearlyTotal / 12;
    
    // Build JSON result
    ResultJSON := TStringList.Create;
    try
      ResultJSON.Add('{');
      ResultJSON.Add(Format('  "year": %d,', [Year]));
      ResultJSON.Add('  "monthlyTotals": [');
      for i := 1 to 12 do
      begin
        ResultJSON.Add(Format('    %s%.2f', 
          [IfThen(i > 1, ','), MonthlyTotals[i]]));
      end;
      ResultJSON.Add('  ],');
      ResultJSON.Add(Format('  "yearlyTotal": %.2f,', [YearlyTotal]));
      ResultJSON.Add(Format('  "averageMonthly": %.2f,', [MonthlyAvg]));
      ResultJSON.Add(Format('  "bestMonth": {', []));
      ResultJSON.Add(Format('    "month": %d,', [BestMonth]));
      ResultJSON.Add(Format('    "amount": %.2f', [BestAmount]));
      ResultJSON.Add('  }');
      ResultJSON.Add('}');
      
      Result := ResultJSON.Text;
    finally
      ResultJSON.Free;
    end;
  finally
    PaymentTotals.Free;
  end;
end;

function TSnpReport.GeneratePaymentMethodsData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  PaymentTotals: TStringList;
  i, j: Integer;
  ResultJSON: TStringList;
  PaymentMethod: string;
  Total: Double;
begin
  if Month > 0 then
    Sessions := FDatabase.GetSessionsByMonth(Year, Month)
  else
    Sessions := FDatabase.GetSessionsByYear(Year);
    
  PaymentTotals := TStringList.Create;
  try
    // Calculate payment method totals
    for i := 0 to High(Sessions) do
    begin
      PaymentMethod := Sessions[i].PaymentMethod;
      if PaymentMethod = '' then
        PaymentMethod := 'Unknown';
        
      j := PaymentTotals.IndexOf(PaymentMethod);
      if j = -1 then
      begin
        j := PaymentTotals.Add(PaymentMethod);
        PaymentTotals.Objects[j] := TObject(0);
      end;
      
      PaymentTotals.Objects[j] := TObject(Integer(PaymentTotals.Objects[j]) + 
        Trunc(Sessions[i].FinalAmount * 100));
    end;
    
    // Build JSON result
    ResultJSON := TStringList.Create;
    try
      ResultJSON.Add('{');
      ResultJSON.Add('  "labels": [');
      for i := 0 to PaymentTotals.Count - 1 do
      begin
        ResultJSON.Add(Format('    "%s"%s', 
          [EscapeJSON(PaymentTotals[i]), 
           IfThen(i < PaymentTotals.Count - 1, ',', '')]));
      end;
      ResultJSON.Add('  ],');
      ResultJSON.Add('  "data": [');
      for i := 0 to PaymentTotals.Count - 1 do
      begin
        Total := Integer(PaymentTotals.Objects[i]) / 100;
        ResultJSON.Add(Format('    %.2f%s', 
          [Total, IfThen(i < PaymentTotals.Count - 1, ',', '')]));
      end;
      ResultJSON.Add('  ]');
      ResultJSON.Add('}');
      
      Result := ResultJSON.Text;
    finally
      ResultJSON.Free;
    end;
  finally
    PaymentTotals.Free;
  end;
end;

procedure TSnpReport.GenerateYearlyReport(Year: Integer; const OutputDir: string);
var
  Template, OutputFile: string;
  Output: TStringList;
  YearlyData, PaymentData: string;
begin
  // Generate data
  YearlyData := GenerateYearlyData(Year);
  PaymentData := GeneratePaymentMethodsData(Year, 0);
  
  // Load and process template
  Template := LoadTemplate('yearly.html');
  
  // Replace placeholders
  Template := StringReplace(Template, '{{year}}', IntToStr(Year), [rfReplaceAll]);
  Template := StringReplace(Template, '{{generated_date}}', 
    FormatDateTime('yyyy-mm-dd hh:nn:ss', Now), [rfReplaceAll]);
  Template := StringReplace(Template, '{{monthly_data}}', YearlyData, [rfReplaceAll]);
  Template := StringReplace(Template, '{{payment_data}}', PaymentData, [rfReplaceAll]);
  
  // Save output
  ForceDirectories(OutputDir);
  OutputFile := IncludeTrailingPathDelimiter(OutputDir) + 
    Format('yearly_report_%d.html', [Year]);
    
  Output := TStringList.Create;
  try
    Output.Text := Template;
    Output.SaveToFile(OutputFile);
  finally
    Output.Free;
  end;
end;

procedure TSnpReport.GenerateMonthlyReport(Year, Month: Integer; const OutputDir: string);
var
  Template, OutputFile: string;
  Output: TStringList;
  MonthlyData, PaymentData, DailyData, HourlyData: string;
begin
  // Generate data
  MonthlyData := GenerateMonthlyData(Year, Month);
  PaymentData := GeneratePaymentMethodsData(Year, Month);
  DailyData := GenerateDailySalesData(Year, Month);
  HourlyData := GenerateHourlyData(Year, Month);
  
  // Load and process template
  Template := LoadTemplate('monthly.html');
  
  // Replace placeholders
  Template := StringReplace(Template, '{{year}}', IntToStr(Year), [rfReplaceAll]);
  Template := StringReplace(Template, '{{month}}', IntToStr(Month), [rfReplaceAll]);
  Template := StringReplace(Template, '{{generated_date}}', 
    FormatDateTime('yyyy-mm-dd hh:nn:ss', Now), [rfReplaceAll]);
  Template := StringReplace(Template, '{{monthly_data}}', MonthlyData, [rfReplaceAll]);
  Template := StringReplace(Template, '{{payment_data}}', PaymentData, [rfReplaceAll]);
  Template := StringReplace(Template, '{{daily_data}}', DailyData, [rfReplaceAll]);
  Template := StringReplace(Template, '{{hourly_data}}', HourlyData, [rfReplaceAll]);
  
  // Save output
  ForceDirectories(OutputDir);
  OutputFile := IncludeTrailingPathDelimiter(OutputDir) + 
    Format('monthly_report_%d_%0.2d.html', [Year, Month]);
    
  Output := TStringList.Create;
  try
    Output.Text := Template;
    Output.SaveToFile(OutputFile);
  finally
    Output.Free;
  end;
end;

function TSnpReport.GenerateMonthlyData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  StartDate, CurrentDate: TDateTime;
  DaysInMonth, i: Integer;
  DailyTotals: array of Double;
  TotalSales, DailySum: Double;
  BusiestDay: Integer;
  BusiestAmount: Double;
  ResultJSON: TStringList;
  Day, M, Y: Word;
begin
  Sessions := FDatabase.GetSessionsByMonth(Year, Month);
  
  // Calculate days in month
  DaysInMonth := DaysInAMonth(Year, Month);
  SetLength(DailyTotals, DaysInMonth + 1); // 1-based array
  
  // Initialize totals
  TotalSales := 0;
  BusiestAmount := 0;
  BusiestDay := 1;
  
  // Calculate daily totals
  for i := 0 to High(Sessions) do
  begin
    DecodeDate(Sessions[i].StartTime, Y, M, Day);
    if (Y = Year) and (M = Month) and (Day <= DaysInMonth) then
    begin
      DailyTotals[Day] := DailyTotals[Day] + Sessions[i].FinalAmount;
      TotalSales := TotalSales + Sessions[i].FinalAmount;
      
      if DailyTotals[Day] > BusiestAmount then
      begin
        BusiestAmount := DailyTotals[Day];
        BusiestDay := Day;
      end;
    end;
  end;
  
  // Calculate average daily sales (excluding days with no sales)
  DailySum := 0;
  for i := 1 to DaysInMonth do
    DailySum := DailySum + DailyTotals[i];
    
  // Build JSON result
  ResultJSON := TStringList.Create;
  try
    ResultJSON.Add('{');
    ResultJSON.Add(Format('  "year": %d,', [Year]));
    ResultJSON.Add(Format('  "month": %d,', [Month]));
    ResultJSON.Add(Format('  "totalSales": %.2f,', [TotalSales]));
    ResultJSON.Add(Format('  "averageDaily": %.2f,', [DailySum / DaysInMonth]));
    ResultJSON.Add('  "busiestDay": {');
    ResultJSON.Add(Format('    "day": %d,', [BusiestDay]));
    ResultJSON.Add(Format('    "amount": %.2f', [BusiestAmount]));
    ResultJSON.Add('  }');
    ResultJSON.Add('}');
    
    Result := ResultJSON.Text;
  finally
    ResultJSON.Free;
  end;
end;

function TSnpReport.GenerateDailySalesData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  DaysInMonth, i, Day: Integer;
  DailyTotals: array of Double;
  DailyDates: array of string;
  Y, M, D: Word;
  CurrentDate: TDateTime;
  ResultJSON: TStringList;
begin
  Sessions := FDatabase.GetSessionsByMonth(Year, Month);
  DaysInMonth := DaysInAMonth(Year, Month);
  
  SetLength(DailyTotals, DaysInMonth + 1); // 1-based array
  SetLength(DailyDates, DaysInMonth + 1);
  
  // Initialize daily totals and dates
  for i := 1 to DaysInMonth do
  begin
    CurrentDate := EncodeDate(Year, Month, i);
    DailyDates[i] := FormatDateTime('yyyy-mm-dd', CurrentDate);
    DailyTotals[i] := 0;
  end;
  
  // Calculate daily totals
  for i := 0 to High(Sessions) do
  begin
    DecodeDate(Sessions[i].StartTime, Y, M, D);
    if (Y = Year) and (M = Month) and (D <= DaysInMonth) then
      DailyTotals[D] := DailyTotals[D] + Sessions[i].FinalAmount;
  end;
  
  // Build JSON result
  ResultJSON := TStringList.Create;
  try
    ResultJSON.Add('{');
    ResultJSON.Add('  "dates": [');
    for i := 1 to DaysInMonth do
    begin
      ResultJSON.Add(Format('    "%s"%s', 
        [DailyDates[i], IfThen(i < DaysInMonth, ',', '')]));
    end;
    ResultJSON.Add('  ],');
    ResultJSON.Add('  "amounts": [');
    for i := 1 to DaysInMonth do
    begin
      ResultJSON.Add(Format('    %.2f%s', 
        [DailyTotals[i], IfThen(i < DaysInMonth, ',', '')]));
    end;
    ResultJSON.Add('  ]');
    ResultJSON.Add('}');
    
    Result := ResultJSON.Text;
  finally
    ResultJSON.Free;
  end;
end;

function TSnpReport.GenerateHourlyData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  HourlyTotals: array[0..23] of Double;
  HourlyCounts: array[0..23] of Integer;
  i, Hour: Integer;
  CurrentDate: TDateTime;
  H, M, S, MS: Word;
  ResultJSON: TStringList;
  j: Integer;
  HourlyAverages: array[0..23] of Double;
begin
  Sessions := FDatabase.GetSessionsByMonth(Year, Month);
  
  // Initialize arrays
  for i := 0 to 23 do
  begin
    HourlyTotals[i] := 0;
    HourlyCounts[i] := 0;
  end;
  
  // Calculate hourly totals and counts
  for i := 0 to High(Sessions) do
  begin
    DecodeTime(Sessions[i].StartTime, H, M, S, MS);
    Hour := H;
    HourlyTotals[Hour] := HourlyTotals[Hour] + Sessions[i].FinalAmount;
    Inc(HourlyCounts[Hour]);
  end;
  
  // Calculate hourly averages
  for i := 0 to 23 do
  begin
    if HourlyCounts[i] > 0 then
      HourlyAverages[i] := HourlyTotals[i] / HourlyCounts[i]
    else
      HourlyAverages[i] := 0;
  end;
  
  // Build JSON result
  ResultJSON := TStringList.Create;
  try
    ResultJSON.Add('{');
    ResultJSON.Add('  "hourlyAverages": [');
    for i := 0 to 23 do
    begin
      ResultJSON.Add(Format('    %.2f%s', 
        [HourlyAverages[i], IfThen(i < 23, ',', '')]));
    end;
    ResultJSON.Add('  ]');
    ResultJSON.Add('}');
    
    Result := ResultJSON.Text;
  finally
    ResultJSON.Free;
  end;
end;

end.
