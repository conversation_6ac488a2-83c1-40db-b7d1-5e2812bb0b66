@echo off
setlocal

echo Rebuilding application...
fpc -B -MObjFPC -Scghi -O1 -g -gl -l -vewnhibq -Fi"lib/x86_64-win64" -Fu"lib/x86_64-win64" -FU"lib/x86_64-win64" -FE. -k"--subsystem console" ReportGenerator.lpr

if %ERRORLEVEL% NEQ 0 (
    echo Build failed with error level %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

echo.
echo ===== TESTING WITH YEAR 2025 =====
ReportGenerator.exe --year 2025

if %ERRORLEVEL% NEQ 0 (
    echo Test failed with error level %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

echo.
echo ===== TESTING WITH YEAR 2025 AND MONTH 8 =====
ReportGenerator.exe --year 2025 --month 8

if %ERRORLEVEL% NEQ 0 (
    echo Test failed with error level %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

echo.
echo All tests completed successfully.
endlocal
