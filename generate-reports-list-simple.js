import { writeFileSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { glob } from 'glob';

const __dirname = fileURLToPath(new URL('.', import.meta.url));

async function main() {
  try {
    // Search for all HTML files in the reports directory
    const files = await glob('reports/**/*.html', { absolute: true });
    
    console.log('Found files:', files);
    
    const reports = {
      lastUpdated: new Date().toISOString(),
      yearly: [],
      monthly: {}
    };

    files.forEach(file => {
      const filename = file.split(/[\\/]/).pop();
      console.log('Processing file:', filename);
      
      // Match yearly reports: yearly_report_YYYY.html
      const yearlyMatch = filename.match(/^yearly_report_(\d{4})\.html$/);
      // Match monthly reports: monthly_report_YYYY_MM.html
      const monthlyMatch = filename.match(/^monthly_report_(\d{4})_(\d{2})\.html$/);

      if (yearlyMatch) {
        const year = parseInt(yearlyMatch[1], 10);
        if (!reports.yearly.includes(year)) {
          reports.yearly.push(year);
        }
      } else if (monthlyMatch) {
        const year = parseInt(monthlyMatch[1], 10);
        const month = parseInt(monthlyMatch[2], 10);
        
        if (!reports.monthly[year]) {
          reports.monthly[year] = [];
        }
        if (!reports.monthly[year].includes(month)) {
          reports.monthly[year].push(month);
        }
        
        // If this year has a yearly report, add the yearly report indicator (0)
        if (reports.yearly.includes(year) && !reports.monthly[year].includes(0)) {
          reports.monthly[year].unshift(0); // Add to beginning of array
        }
      }
    });

    // Sort years in descending order
    reports.yearly.sort((a, b) => b - a);
    
    // Sort months in ascending order for each year
    Object.values(reports.monthly).forEach(months => months.sort((a, b) => a - b));

    // Write the reports list to a JSON file
    const outputFile = join(__dirname, 'reports', 'reports.json');
    writeFileSync(outputFile, JSON.stringify(reports, null, 2));
    
    console.log('Generated reports.json:');
    console.log(JSON.stringify(reports, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
