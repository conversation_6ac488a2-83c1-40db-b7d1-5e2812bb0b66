[SMTP]
Host=smtp.gmail.com
Port=587
UseSSL=true
Username=<EMAIL>
Password=your-app-password
FromEmail=<EMAIL>

[SMTP_INSTRUCTIONS]
Host=SMTP server (e.g., smtp.gmail.com, smtp.outlook.com)
Port=587 for TLS, 465 for SSL, 25 for plain
UseSSL=true for secure connection, false for plain
Username=Your email address
Password=Your email password or app password
FromEmail=Email address to send from (usually same as <PERSON>rna<PERSON>)
Gmail_Note=For Gmail use App Password, not regular password
Outlook_Note=For Outlook use smtp.outlook.com port 587

[EMAIL_PROVIDERS]
Gmail_Host=smtp.gmail.com
Gmail_Port=587
Gmail_SSL=true
Gmail_Note=Requires App Password (not regular password)

Outlook_Host=smtp.outlook.com
Outlook_Port=587
Outlook_SSL=true
Outlook_Note=Use your Microsoft account credentials

Yahoo_Host=smtp.mail.yahoo.com
Yahoo_Port=587
Yahoo_SSL=true
Yahoo_Note=Requires App Password

[SETUP_INSTRUCTIONS]
Step1=Copy this file to config.ini
Step2=Edit the [SMTP] section with your email provider settings
Step3=For Gmail: Enable 2FA and create an App Password
Step4=For Outlook: Use your regular Microsoft account credentials
Step5=Test the configuration by running the report generator

[GMAIL_APP_PASSWORD_SETUP]
Step1=Go to your Google Account settings (myaccount.google.com)
Step2=Click on Security in the left sidebar
Step3=Enable 2-Step Verification if not already enabled
Step4=Under 2-Step Verification, click on App passwords
Step5=Select app: Mail, Select device: Windows Computer
Step6=Click Generate and copy the 16-character password
Step7=Use this App Password (not your regular password) in config.ini
Note=App passwords only work with 2-Step Verification enabled

[OutputDirectory]
; This will be automatically set based on database location
; You can override it here if needed
; OutputDirectory=C:\Reports

[EmailRecipient]
; This is currently <NAME_EMAIL>
; Future versions may make this configurable
