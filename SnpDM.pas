unit SnpDM;

{$mode objfpc}{$H+}

interface

uses
  SysUtils, Classes, SQLDB, DB, SQLite3Conn, IBConnection,
  Forms, ExtCtrls, Dialogs, SnpTypes, DCPrijndael, DCPsha256,
  ShlObj,IniFiles;

type
  { TSnpDM }
  TSnpDM = class(TDataModule)
    SQConnection: TSQLite3Connection;
    FBConnection: TIBConnection;
    SQLTransaction: TSQLTransaction;
    FBTransaction: TSQLTransaction;
    SQLQuery: TSQLQuery;
    FBQuery: TSQLQuery;
    procedure DataModuleCreate(Sender: TObject);
  private

    function GetHiddenString: string;
  public
    procedure InitializeDatabase;
    function GetAppDataPath(IsNetworkPath: Boolean): string;
    function LoadSettings: TStringList;
    function LoadCaptions: TStringList;
    function GetSettingValue(const Key: string): string;
    procedure SaveSettingValue(const Key, Value: string);
    procedure LoadPlayers;
    procedure SavePlayer(NationalId, PlayerName, Phone, Email: String; 
      MembershipLevel: String; TotalPurchases: Double; ReferrerId: Integer; PlayerImage: TStream; out PlayerId: Integer);
    procedure UpdatePlayerTotalPurchases(PlayerId: Integer; TotalPurchases: Double);
    function FindPlayerById(Id: Integer): TPlayer;
    function FindPlayerByName(PlayerName: String): TPlayer;
    function GetDownline(PlayerId: Integer): TList;
    function FindPlayerIdByName(PlayerName: String): Integer;
    function CreateNewSession(TableID: Integer; UserID: Integer): Integer;
    procedure SaveSessionWithDetails(
      SessionID: Integer;
      EndTime: TDateTime;
      TotalAmount, MemberDiscount, OperatorDiscount, FinalAmount: Double;
      PlayerID: Integer;
      PaymentMethod: string
    );
   procedure GetSessionDetails(
  SessionID: Integer;
  out TotalAmount, MemberDiscount, OperatorDiscount, FinalAmount: Double;
  out PaymentMethod, PlayerName: string;
  out StartTime, EndTime: TDateTime  // Added output parameters
);
    function GetOpenSessions: TSessionArray;
    function GetAllPoolTables: TPoolTableArray;
    function GetAllPlayers: TList;
 
end;
var
  DM: TSnpDM;

implementation

{$R *.lfm}

function TSnpDM.GetHiddenString: string;
var
  EncryptedStr: AnsiString;
  Cipher: TDCP_rijndael;
  KeyStr: AnsiString;  
begin

  EncryptedStr := 'fvI3onazRl1yHuNCwqbTQCdXjaxhQsfkkDCV';  
  KeyStr := 'SHA512' + IntToStr(99036);
  Cipher := TDCP_rijndael.Create(nil);
  try
    Cipher.InitStr(KeyStr, TDCP_sha256);
    Result := string(Cipher.DecryptString(EncryptedStr));
  finally
    Cipher.Free;
  end;
end;

function TSnpDM.GetAppDataPath(IsNetworkPath: Boolean): string;
var
  NetworkPath: string;
  Path: array[0..MAX_PATH] of Char;
begin
  if IsNetworkPath then
  begin
    // Read network path from config.ini
    if FileExists(ExtractFilePath(ParamStr(0)) + 'snpconfig.ini') then
    begin
      with TIniFile.Create(ExtractFilePath(ParamStr(0)) + 'snpconfig.ini') do
      try
        NetworkPath := ReadString('Database', 'NetworkPath', '');
        Result := IncludeTrailingPathDelimiter(NetworkPath);
          Exit;
      finally
        Free;
      end;
    end;
  end;

  // Try LocalAppData next
  if SHGetFolderPath(0, CSIDL_LOCAL_APPDATA, 0, 0, @Path) = S_OK then
  begin
    Result := IncludeTrailingPathDelimiter(Path) + 'SNP\data\';
    if DirectoryExists(Result) and FileExists(Result + 'esnpdb.db') then Exit;
  end;
  
  // Try CommonAppData next
  if SHGetFolderPath(0, CSIDL_COMMON_APPDATA, 0, 0, @Path) = S_OK then
  begin
    Result := IncludeTrailingPathDelimiter(Path) + 'SNP\data\';    
    if DirectoryExists(Result) and FileExists(Result + 'esnpdb.db') then Exit;
  end;
  
  // Fall back to application directory
  Result := ExtractFilePath(Application.ExeName) + 'data\';

end;

procedure TSnpDM.DataModuleCreate(Sender: TObject);
begin  
  // Set database paths
  SQConnection.DatabaseName := GetAppDataPath(false) + 'esnpdb.db';
  FBConnection.DatabaseName := GetAppDataPath(true) + 'snpdb.fdb';

  FBConnection.Username := 'SYSDBA';
  FBConnection.Password := 'masterkey';
  InitializeDatabase;
end;

procedure TSnpDM.InitializeDatabase;
begin
  try
    SQConnection.KeepConnection := True;
    SQConnection.Connected := True;

    // Start transaction only when needed
    if not SQLTransaction.Active then
      SQLTransaction.StartTransaction;
    try
      SQConnection.ExecuteDirect(GetHiddenString);

      // Check if tables exist
      SQLQuery.SQL.Text :=
        'SELECT name FROM sqlite_master';
      SQLQuery.Open;
      try
        if SQLQuery.RecordCount < 2 then
          ShowMessage('Required DATA not found');
      finally
        SQLQuery.Close;
      end;
      SQLTransaction.Commit;
    except
      if SQLTransaction.Active then
        SQLTransaction.Rollback;
      raise;
    end;
    //SQConnection.ExecuteDirect('PRAGMA journal_mode=WAL;');  // Enable WAL mode
    //SQConnection.ExecuteDirect('PRAGMA synchronous=NORMAL;');

    // Initialize Firebird
    FBConnection.Connected := True;

    // Start transaction only when needed
    if not FBTransaction.Active then
      FBTransaction.StartTransaction;
    try
      // Verify Firebird connection
      FBQuery.SQL.Text := 'SELECT 1 FROM RDB$DATABASE';
      FBQuery.Open;
      FBQuery.Close;

      FBTransaction.Commit;
    except
      if FBTransaction.Active then
        FBTransaction.Rollback;
      raise;
    end;

  except
    on E: Exception do
    begin
      ShowMessage('Initialization error: '+ E.Message);
    end;
  end;
end;

function TSnpDM.LoadSettings: TStringList;
var
  SettingsList: TStringList;
begin
  //InitializeDatabase;
  SettingsList := TStringList.Create;
  try
    SQLQuery.SQL.Text := 'SELECT key, value FROM settings';
    SQLQuery.Open;
    try
      while not SQLQuery.EOF do
      begin
        // Add each setting to SettingsList with trimmed keys
        SettingsList.Values[Trim(SQLQuery.FieldByName('key').AsString)] := SQLQuery.FieldByName('value').AsString;
        SQLQuery.Next;
      end;
    finally
      SQLQuery.Close;
    end;
  except
    on E: Exception do
    begin
      SettingsList.Free;
      ShowMessage('Failed to load settings: '+E.Message);
    end;
  end;
  Result := SettingsList;
end;

function TSnpDM.LoadCaptions: TStringList;
var
  CaptionList: TStringList;
begin
  CaptionList := TStringList.Create;
  try
    SQLQuery.SQL.Text := 'SELECT key, captext FROM settings';
    SQLQuery.Open;
    try
      while not SQLQuery.EOF do
      begin
        CaptionList.Values[SQLQuery.FieldByName('key').AsString] := SQLQuery.FieldByName('captext').AsString;
        SQLQuery.Next;
      end;
    finally
      SQLQuery.Close;
    end;
  except
    on E: Exception do
    begin
      CaptionList.Free;
      ShowMessage('Failed to load captions: '+E.Message);
    end;
  end;
  Result := CaptionList;
end;

function TSnpDM.GetSettingValue(const Key: string): string;
begin
  Result := '';
  try
    SQLQuery.SQL.Text := 'SELECT value FROM settings WHERE key = :key LIMIT 1';
    SQLQuery.ParamByName('key').AsString := Key;
    SQLQuery.Open;
    try
      if not SQLQuery.EOF then
        Result := SQLQuery.FieldByName('value').AsString;
    finally
      SQLQuery.Close;
    end;
  except
    on E: Exception do
      raise Exception.Create('Database query error: ' + E.Message);
  end;
end;

procedure TSnpDM.SaveSettingValue(const Key, Value: string);
begin
  try
    if not SQLTransaction.Active then
      SQLTransaction.StartTransaction;  // Add transaction check
    try
      SQLQuery.SQL.Text := 
        'UPDATE settings SET value = :value WHERE key = :key';
      SQLQuery.ParamByName('key').AsString := Key;
      SQLQuery.ParamByName('value').AsString := Value;
      SQLQuery.ExecSQL;
      SQLTransaction.Commit;
    except
      on E: Exception do
      begin
        if SQLTransaction.Active then
          SQLTransaction.Rollback;
        raise;  // Re-raise exception after rollback
      end;
    end;
  finally
    if SQLQuery.Active then
      SQLQuery.Close;
  end;
end;

procedure TSnpDM.LoadPlayers;
begin
  try
    FBQuery.SQL.Text := 'SELECT * FROM players';
    FBQuery.Open;
    try
      while not FBQuery.EOF do
      begin
        FBQuery.Next;
      end;
    finally
      FBQuery.Close;
    end;
  except
    on E: Exception do
      ShowMessage('Failed to load Members: ');
  end;
end;

procedure TSnpDM.SavePlayer(NationalId, PlayerName, Phone, Email: String; 
      MembershipLevel: String; TotalPurchases: Double; ReferrerId: Integer; PlayerImage: TStream; out PlayerId: Integer);
begin
  if not Assigned(FBQuery) or not Assigned(FBTransaction) then
    raise Exception.Create('Database components not initialized');

  if PlayerName.Trim.IsEmpty then
    raise Exception.Create('Player name cannot be empty');

  try
    if not FBTransaction.Active then
      FBTransaction.StartTransaction;

    // Validate referrer exists if specified
    if (ReferrerId > 0) then
    begin
      FBQuery.SQL.Text := 'SELECT 1 FROM players WHERE id = :referrer_id';
      FBQuery.ParamByName('referrer_id').AsInteger := ReferrerId;
      FBQuery.Open;
      try
        if FBQuery.EOF then
          ReferrerId := 0; // Reset to 0 if referrer doesn't exist
      finally
        FBQuery.Close;
      end;
    end;

    FBQuery.SQL.Text := 'INSERT INTO players (national_id, playername, phone, ' +
      'email, membership_level, total_purchases, referrer_id, member_since, ' +
      'referral_count, loyalty_points, picture) VALUES (:national_id, ' +
      ':playername, :phone, :email, :membership_level, :total_purchases, ' +
      ':referrer_id, :member_since, 0, 0, :picture) RETURNING id';
      
    FBQuery.Params.ParamByName('national_id').AsString := NationalId;
    FBQuery.Params.ParamByName('playername').AsString := PlayerName;
    FBQuery.Params.ParamByName('phone').AsString := Phone;
    FBQuery.Params.ParamByName('email').AsString := Email;
    FBQuery.Params.ParamByName('membership_level').AsString := MembershipLevel;
    FBQuery.Params.ParamByName('total_purchases').AsFloat := TotalPurchases;
    FBQuery.Params.ParamByName('member_since').AsDate := Date;
  
    if ReferrerId > 0 then
      FBQuery.Params.ParamByName('referrer_id').AsInteger := ReferrerId
    else
      FBQuery.Params.ParamByName('referrer_id').Clear; // Use NULL instead of 0
      
    
    if Assigned(PlayerImage) and (PlayerImage.Size > 0) then
    begin
      PlayerImage.Position := 0;
      FBQuery.Params.ParamByName('picture').LoadFromStream(PlayerImage, ftBlob)
    end
    else
      FBQuery.Params.ParamByName('picture').Clear;
    
    try
      FBQuery.Open;  // Changed from ExecSQL to Open for RETURNING clause
      try
        if not FBQuery.EOF then
          PlayerId := FBQuery.FieldByName('id').AsInteger
        else
          raise Exception.Create('Failed to retrieve new player ID');
      finally
        FBQuery.Close;
      end;
      
      FBTransaction.Commit;
    except
      on E: Exception do
      begin
        if FBTransaction.Active then
          FBTransaction.Rollback;
        raise;
      end;
    end;
  except
    on E: Exception do
      raise Exception.Create('Failed to save player: ' + E.Message);
  end;
end;

function TSnpDM.FindPlayerById(Id: Integer): TPlayer;
var 
  ReferrerId: Integer;
begin
  Result := nil;
  if Id <= 0 then Exit;
  
  try
    FBQuery.SQL.Text := 'SELECT * FROM players WHERE id = :id';
    FBQuery.Params.ParamByName('id').AsInteger := Id;
    FBQuery.Open;
    try
      if not FBQuery.EOF then
      begin
        try
          ReferrerId := FBQuery.FieldByName('referrer_id').AsInteger;
          Result := TPlayer.Create(
            FBQuery.FieldByName('national_id').AsString,
            FBQuery.FieldByName('playername').AsString,
            FBQuery.FieldByName('membership_level').AsString,
            ReferrerId
          );
          
          Result.Id := Id;
          Result.TotalPurchases := FBQuery.FieldByName('total_purchases').AsFloat;
          Result.MemberSince := StrToDate(FBQuery.FieldByName('member_since').AsString);
          Result.ReferralCount := FBQuery.FieldByName('referral_count').AsInteger;
          Result.LoyaltyPoints := FBQuery.FieldByName('loyalty_points').AsInteger;
          Result.Phone := FBQuery.FieldByName('phone').AsString;
          Result.Email := FBQuery.FieldByName('email').AsString;
        except
          FreeAndNil(Result);
          raise;
        end;
      end;
    finally
      FBQuery.Close;
    end;
  except
    on E: Exception do
      raise Exception.Create('Failed to find player by ID: ' + E.Message);
  end;
end;

function TSnpDM.FindPlayerByName(PlayerName: String): TPlayer;
begin
  Result := nil;
  try
    FBQuery.SQL.Text := 'SELECT * FROM players WHERE UPPER(playername) LIKE UPPER(:playername)';
    FBQuery.Params.ParamByName('playername').AsString := '%' + PlayerName + '%';
    FBQuery.Open;
    try
      if not FBQuery.EOF then
      begin
        try
          Result := TPlayer.Create(
            FBQuery.FieldByName('national_id').AsString,
            FBQuery.FieldByName('playername').AsString,
            FBQuery.FieldByName('membership_level').AsString,
            FBQuery.FieldByName('referrer_id').AsInteger
          );
          Result.Id := FBQuery.FieldByName('id').AsInteger;
          Result.TotalPurchases := FBQuery.FieldByName('total_purchases').AsFloat;
          Result.MemberSince := StrToDate(FBQuery.FieldByName('member_since').AsString);
          Result.ReferralCount := FBQuery.FieldByName('referral_count').AsInteger;
          Result.LoyaltyPoints := FBQuery.FieldByName('loyalty_points').AsInteger;
          Result.Phone := FBQuery.FieldByName('phone').AsString;
          Result.Email := FBQuery.FieldByName('email').AsString;
        except
          FreeAndNil(Result);
          raise;
        end;
      end;
    finally
      FBQuery.Close;
    end;
  except
    on E: Exception do
      raise Exception.Create('Failed to find player by name: ' + E.Message);
  end;
end;

function TSnpDM.FindPlayerIdByName(PlayerName: String): Integer;
begin
  Result := 0;
  try
    FBQuery.SQL.Text := 'SELECT id FROM players WHERE UPPER(playername) LIKE UPPER(:playername)';
    FBQuery.Params.ParamByName('playername').AsString := '%' + PlayerName + '%';
    FBQuery.Open;
    try
      if not FBQuery.EOF then
      begin
        Result := FBQuery.FieldByName('id').AsInteger;
      end;
    finally
      FBQuery.Close;
    end;
  except
    on E: Exception do
      raise Exception.Create('Failed to find player ID by name: ' + E.Message);
  end;
end;

procedure TSnpDM.UpdatePlayerTotalPurchases(PlayerId: Integer; TotalPurchases: Double);
begin
  try
    if not FBTransaction.Active then
      FBTransaction.StartTransaction;
      
    FBQuery.SQL.Text := 'UPDATE players SET total_purchases = :total_purchases WHERE id = :id';
    FBQuery.Params.ParamByName('total_purchases').AsFloat := TotalPurchases;
    FBQuery.Params.ParamByName('id').AsInteger := PlayerId;
    FBQuery.ExecSQL;
    FBTransaction.Commit;
  except
    on E: Exception do
    begin
      if FBTransaction.Active then
        FBTransaction.Rollback;
      raise Exception.Create('Failed to update player purchases: ' + E.Message);
    end;
  end;
end;

function TSnpDM.GetDownline(PlayerId: Integer): TList;
var
  DownlineList: TList;
  Player: TPlayer;
begin
  DownlineList := TList.Create;  // Create list first
  Result := DownlineList;        // Assign to Result immediately
  try
    FBQuery.SQL.Text := 'SELECT * FROM players WHERE referrer_id = :referrer_id';
    FBQuery.Params.ParamByName('referrer_id').AsInteger := PlayerId;
    FBQuery.Open;
    try
      while not FBQuery.EOF do
      begin
        Player := TPlayer.Create(
          FBQuery.FieldByName('national_id').AsString,
          FBQuery.FieldByName('playername').AsString,
          FBQuery.FieldByName('membership_level').AsString,
          FBQuery.FieldByName('referrer_id').AsInteger
        );
        Player.Id := FBQuery.FieldByName('id').AsInteger;
        Player.TotalPurchases := FBQuery.FieldByName('total_purchases').AsFloat;
        Player.MemberSince := StrToDate(FBQuery.FieldByName('member_since').AsString);
        Player.ReferralCount := FBQuery.FieldByName('referral_count').AsInteger;
        Player.LoyaltyPoints := FBQuery.FieldByName('loyalty_points').AsInteger;
        Player.Phone := FBQuery.FieldByName('phone').AsString;
        Player.Email := FBQuery.FieldByName('email').AsString;
        DownlineList.Add(Player);
        FBQuery.Next;
      end;
      Result := DownlineList; // Assign Result inside try block
    finally
      FBQuery.Close;
    end;
  except
    on E: Exception do
    begin
      DownlineList.Free;
      raise Exception.Create('Failed to retrieve downline: ' + E.Message);
    end;
  end;
end;

function TSnpDM.GetAllPlayers: TList;
var
  PlayerList: TList;
  Player: TPlayer;
begin
  PlayerList := TList.Create;
  try
    FBQuery.SQL.Text := 'SELECT * FROM players';
    FBQuery.Open;
    try
      while not FBQuery.EOF do
      begin
        Player := TPlayer.Create(
          FBQuery.FieldByName('national_id').AsString,
          FBQuery.FieldByName('playername').AsString,
          FBQuery.FieldByName('membership_level').AsString,
          FBQuery.FieldByName('referrer_id').AsInteger
        );
        Player.Id := FBQuery.FieldByName('id').AsInteger;
        Player.TotalPurchases := FBQuery.FieldByName('total_purchases').AsFloat;
        Player.MemberSince := StrToDate(FBQuery.FieldByName('member_since').AsString);
        Player.ReferralCount := FBQuery.FieldByName('referral_count').AsInteger;
        Player.LoyaltyPoints := FBQuery.FieldByName('loyalty_points').AsInteger;
        Player.Phone := FBQuery.FieldByName('phone').AsString;
        Player.Email := FBQuery.FieldByName('email').AsString;
        PlayerList.Add(Player);
        FBQuery.Next;
      end;
    finally
      FBQuery.Close;
    end;
  except
    PlayerList.Free;
    raise;
  end;
  Result := PlayerList;
end;

function TSnpDM.CreateNewSession(TableID: Integer; UserID: Integer): Integer;
begin
  Result := -1;
  if not FBTransaction.Active then
    FBTransaction.StartTransaction;
  try
    with FBQuery do
    begin
      Close;
      SQL.Text := 'INSERT INTO SESSIONS (TABLE_ID, OPERATOR_ID, START_TIME) ' +
                  'VALUES (:TABLE_ID, :OPERATOR_ID, CURRENT_TIMESTAMP) RETURNING ID';
      ParamByName('TABLE_ID').AsInteger := TableID;
      ParamByName('OPERATOR_ID').AsInteger := UserID;
      Open;
      try
        if not EOF then
          Result := FieldByName('ID').AsInteger;
      finally
        Close;
      end;
    end;
    FBTransaction.Commit;
  except
    FBTransaction.Rollback;
    raise;
  end;
end;

procedure TSnpDM.SaveSessionWithDetails(
  SessionID: Integer;
  EndTime: TDateTime;
  TotalAmount, MemberDiscount, OperatorDiscount, FinalAmount: Double;
  PlayerID: Integer;
  PaymentMethod: string
);
const
  SQL_UPDATE = 'UPDATE sessions SET end_time = :end_time, total_amount = :total_amount, ' +
               'member_discount = :member_discount, operator_discount = :operator_discount, ' +
               'final_amount = :final_amount, player_id = :player_id, ' +
               'payment_method = :payment_method WHERE id = :session_id';
begin
  // Validate inputs
  if SessionID <= 0 then
    raise Exception.Create('Invalid session ID');
  if TotalAmount < 0 then
    raise Exception.Create('Total amount cannot be negative');
  if FinalAmount < 0 then
    raise Exception.Create('Final amount cannot be negative');
  if PaymentMethod = '' then
    raise Exception.Create('Payment method cannot be empty');

  try
    if not FBTransaction.Active then
      FBTransaction.StartTransaction;

    FBQuery.SQL.Text := SQL_UPDATE;
    with FBQuery.Params do
    begin
      ParamByName('end_time').AsDateTime := EndTime;
      ParamByName('total_amount').AsFloat := TotalAmount;
      ParamByName('member_discount').AsFloat := MemberDiscount;
      ParamByName('operator_discount').AsFloat := OperatorDiscount;
      ParamByName('final_amount').AsFloat := FinalAmount;
      if PlayerID > 0 then
        ParamByName('player_id').AsInteger := PlayerID
      else
        ParamByName('player_id').Clear;
      ParamByName('payment_method').AsString := PaymentMethod;
      ParamByName('session_id').AsInteger := SessionID;
    end;
    
    FBQuery.ExecSQL;
    
    if FBQuery.RowsAffected = 0 then
      raise Exception.Create('No session updated. Invalid session ID?');
    
    FBTransaction.Commit;  // Explicit commit after successful update
  except
    on E: Exception do
    begin
      if FBTransaction.Active then
        FBTransaction.Rollback;
      raise Exception.Create('Failed to save session: ' + E.Message);
    end;
  end;
end;

procedure TSnpDM.GetSessionDetails(
  SessionID: Integer;
  out TotalAmount, MemberDiscount, OperatorDiscount, FinalAmount: Double;
  out PaymentMethod, PlayerName: string;
  out StartTime, EndTime: TDateTime  // Added output parameters
);
begin
  try
    FBQuery.SQL.Text := 
      'SELECT s.*, p.playername FROM sessions s ' +
      'LEFT JOIN players p ON s.player_id = p.id ' +
      'WHERE s.id = :session_id';
    
    FBQuery.ParamByName('session_id').AsInteger := SessionID;
    FBQuery.Open;
    
    try
      if not FBQuery.EOF then
      begin
        TotalAmount := FBQuery.FieldByName('total_amount').AsFloat;
        MemberDiscount := FBQuery.FieldByName('member_discount').AsFloat;
        OperatorDiscount := FBQuery.FieldByName('operator_discount').AsFloat;
        FinalAmount := FBQuery.FieldByName('final_amount').AsFloat;
        PaymentMethod := FBQuery.FieldByName('payment_method').AsString;
        StartTime := FBQuery.FieldByName('start_time').AsDateTime;
        EndTime := FBQuery.FieldByName('end_time').AsDateTime;
        
        if FBQuery.FieldByName('playername').IsNull then
          PlayerName := 'Walk-in Customer'
        else
          PlayerName := FBQuery.FieldByName('playername').AsString;
      end;
    finally
      FBQuery.Close;
    end;
  except
    on E: Exception do
      raise Exception.Create('Failed to get session details: ' + E.Message);
  end;
end;

function TSnpDM.GetOpenSessions: TSessionArray;
var
  i: Integer;
begin
  Result := nil;
  try
    if not FBTransaction.Active then
      FBTransaction.StartTransaction;
      
    FBQuery.SQL.Text := 
      'SELECT s.id, s.table_id, s.start_time ' +
      'FROM sessions s ' +
      'WHERE s.end_time IS NULL';
      
    FBQuery.Open;
    try
      SetLength(Result, FBQuery.RecordCount);
      i := 0;
      while not FBQuery.EOF do
      begin
        Result[i].SessionID := FBQuery.FieldByName('id').AsInteger;
        Result[i].TableID := FBQuery.FieldByName('table_id').AsInteger;
        Result[i].StartTime := FBQuery.FieldByName('start_time').AsDateTime;
        Inc(i);
        FBQuery.Next;
      end;
    finally
      FBQuery.Close;
      if FBTransaction.Active then
        FBTransaction.Commit;
    end;
  except
    on E: Exception do
    begin
      if FBTransaction.Active then
        FBTransaction.Rollback;
      raise Exception.Create('Failed to get open sessions: ' + E.Message);
    end;
  end;
end;
  

function TSnpDM.GetAllPoolTables: TPoolTableArray;
var
  i: Integer;
begin
  try
    SQLQuery.SQL.Text := 'SELECT * FROM pooltables ORDER BY id';
    SQLQuery.Open;
    try
      SQLQuery.Last;
      SetLength(Result, SQLQuery.RecordCount);
      SQLQuery.First;
      i := 0;
      while not SQLQuery.EOF do
      begin
        Result[i].TableID := SQLQuery.FieldByName('id').AsInteger;
        Result[i].TableType := SQLQuery.FieldByName('type').AsString;
        Result[i].Category := SQLQuery.FieldByName('category').AsString;
        Result[i].TableName := SQLQuery.FieldByName('tablename').AsString;
        Inc(i);
        SQLQuery.Next;
      end;
    finally
      SQLQuery.Close;
    end;
  except
    on E: Exception do
      raise Exception.Create('Failed to get pool tables: ' + E.Message);
  end;
end;

end.
