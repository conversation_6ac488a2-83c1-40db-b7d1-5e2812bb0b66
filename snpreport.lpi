<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectOptions>
    <Version Value="12"/>
    <PathDelim Value="\"/>
    <General>
      <Flags>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasScaledStatement Value="False"/>
      </Flags>
      <SessionStorage Value="InProjectDir"/>
      <Title Value="SNP Report Generator"/>
      <UseAppBundle Value="False"/>
      <ResourceType Value="res"/>
    </General>
    <BuildModes>
      <Item Name="Default" Default="True"/>
    </BuildModes>
    <PublishOptions>
      <Version Value="2"/>
      <UseFileFilters Value="True"/>
    </PublishOptions>
    <RunParams>
      <FormatVersion Value="2"/>
    </RunParams>
    <RequiredPackages>
      <Item>
        <PackageName Value="laz_synapse"/>
      </Item>
      <Item>
        <PackageName Value="FCL"/>
      </Item>
      <Item>
        <PackageName Value="LCLBase"/>
      </Item>
      <Item>
        <PackageName Value="LCL"/>
      </Item>
    </RequiredPackages>
    <Units>
      <Unit>
        <Filename Value="snpreport.lpr"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uMain.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uDB.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uReports.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="uUtils.pas"/>
        <IsPartOfProject Value="True"/>
      </Unit>
    </Units>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="11"/>
    <PathDelim Value="\"/>
    <Target>
      <Filename Value="bin\snpreport"/>
    </Target>
    <SearchPaths>
      <IncludeFiles Value="$(ProjOutDir)"/>
      <OtherUnitFiles Value=".;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\rtl;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-base;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-json;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-xml;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\fcl-process;D:\lazarus4\fpc\3.2.2\units\x86_64-win64\ibase;D:\lazarus4\components\lazutils\lib\x86_64-win64;D:\lazarus4\lcl\units\x86_64-win64;D:\lazarus4\lcl\units\x86_64-win64\lclbase;D:\lazarus4\components\lazcontrols\lib\x86_64-win64\lcl;C:\Users\<USER>\AppData\Local\lazarus4\onlinepackagemanager\packages\synapse40.1"/>
      <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
    <Parsing>
      <SyntaxOptions>
        <SyntaxMode Value="Delphi"/>
        <CStyleOperator Value="False"/>
        <IncludeAssertionCode Value="True"/>
        <AllowLabel Value="False"/>
        <CPPInline Value="False"/>
      </SyntaxOptions>
    </Parsing>
    <CodeGeneration>
      <SmartLinkUnit Value="True"/>
      <RelocatableUnit Value="False"/>
      <Optimizations>
        <OptimizationLevel Value="1"/>
      </Optimizations>
    </CodeGeneration>
    <Linking>
      <Debugging>
        <DebugInfoType Value="dsDwarf3"/>
        <UseHeaptrc Value="True"/>
        <TrashVariables Value="True"/>
        <UseExternalDbgSyms Value="True"/>
      </Debugging>
      <Options>
        <PassLinkerOptions Value="True"/>
        <LinkerOptions Value="-g"/>
      </Options>
    </Linking>
    <Other>
      <Verbosity>
        <ShowWarn Value="True"/>
        <ShowNotes Value="True"/>
        <ShowHints Value="True"/>
        <ShowInfos Value="True"/>
        <ShowLineNum Value="True"/>
        <ShowAll Value="True"/>
      </Verbosity>
    </Other>
  </CompilerOptions>
</CONFIG>
