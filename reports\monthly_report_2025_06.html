<!DOCTYPE html>
<html>
<head>
  <title>Monthly Report - 6/2025</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .chart-container {
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 500px;
      width: 100%;
    }

    @media (max-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
    .daily-section,
    .payment-section {
      display: flex;
      gap: 30px;
      margin: 20px 0;
      align-items: flex-start;
    }
    .daily-chart,
    .payment-chart {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 600px;
    }
    .daily-table-container,
    .payment-table-container {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      overflow-x: auto;
    }
    .daily-table-container {
      overflow-x: auto; /* Allow horizontal scrolling for wide tables */
    }
    .daily-table,
    .payment-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-size: 0.9em;
    }
    .daily-table {
      font-size: 0.8em; /* Smaller font for more columns */
    }
    .daily-table th,
    .daily-table td,
    .payment-table th,
    .payment-table td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #eee;
    }
    .daily-table th,
    .payment-table th {
      background: #3498db;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }
    .daily-table th:nth-child(n+2),
    .payment-table th:nth-child(n+2) {
      text-align: right;
    }
    .daily-table tr:hover,
    .payment-table tr:hover {
      background: #f8f9fa;
    }
    .daily-table tr:last-child td,
    .payment-table tr:last-child td {
      border-bottom: none;
    }
    .payment-amount {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }
    .payment-percentage {
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-method-name {
      font-weight: 500;
      color: #34495e;
    }
    .table-total {
      background: #ecf0f1 !important;
      font-weight: 600;
      border-top: 2px solid #3498db;
    }
    .summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 1.1em;
    }
    .summary-value {
      font-weight: bold;
      color: #2c3e50;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #7f8c8d;
      font-size: 0.9em;
    }

    /* Toggle Controls */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 20px;
    }

    .section-title {
      margin: 0;
      font-size: 1.5em;
      color: #2c3e50;
    }

    .toggle-control {
      display: inline-flex;
      background: #ecf0f1;
      border-radius: 20px;
      padding: 3px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .toggle-button {
      padding: 6px 16px;
      border: none;
      background: transparent;
      border-radius: 17px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.3s ease;
      color: #7f8c8d;
    }

    .toggle-button.active {
      background: #3498db;
      color: white;
      box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    }

    .toggle-button:hover:not(.active) {
      color: #2c3e50;
    }

    /* Hide/Show functionality */
    .chart-view .table-container,
    .table-view .chart-container-section {
      display: none;
    }

    .chart-view .chart-container-section,
    .table-view .table-container {
      display: block;
      flex: 1;
    }

    /* Full width when only one view is shown */
    .section-content.chart-view .chart-container-section,
    .section-content.table-view .table-container {
      width: 100%;
      max-width: none;
    }

    /* Chart container sizing fixes */
    .chart-container-section {
      position: relative;
      height: 600px;
      max-height: 600px;
      overflow: hidden;
    }

    .chart-container-section canvas {
      max-width: 100% !important;
      max-height: 100% !important;
    }

    @media (max-width: 768px) {
      .section-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }

      .chart-container-section {
        height: 400px;
        max-height: 400px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Monthly Sales Report - 6/2025</h1>
      <p>Generated on 2025-08-17 00:25:36</p>
    </div>

    <div class="summary">
      <h2>Summary</h2>
      <div id="summary-content">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>

    <div class="daily-section">
      <div class="section-header">
        <h2 class="section-title">Daily Sales - 6/2025</h2>
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('daily', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('daily', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="daily-content">
        <div class="chart-container-section daily-chart">
          <div style="position: relative; height: calc(100% - 20px); width: 100%;">
            <canvas id="dailySalesChart"></canvas>
          </div>
        </div>

        <div class="table-container daily-table-container">
          <table class="daily-table" id="dailySalesTable">
            <thead id="dailyTableHeader">
              <!-- Will be populated by JavaScript -->
            </thead>
            <tbody id="dailyTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="payment-section">
      <div class="section-header">
        <h2 class="section-title">Payment Methods - 6/2025</h2>
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('payment', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('payment', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="payment-content">
        <div class="chart-container-section payment-chart">
          <div style="position: relative; height: calc(100% - 20px); width: 100%;">
            <canvas id="paymentMethodsChart"></canvas>
          </div>
        </div>

        <div class="table-container payment-table-container">
          <table class="payment-table" id="paymentMethodsTable">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Percentage</th>
              </tr>
            </thead>
            <tbody id="paymentTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <h2>Hourly Distribution - 6/2025</h2>
      <div style="position: relative; height: calc(100% - 40px); width: 100%;">
        <canvas id="hourlyDistributionChart"></canvas>
      </div>
    </div>

    <div class="footer">
      <p>SnP Report Generator | Generated by Asia Club Management System</p>
    </div>
  </div>

  <script>
    // Parse the JSON data passed from Pascal
    const monthlyData = {
  "year" : 2025,
  "month" : 6,
  "totalSales" : 1.1623433000000000E+005,
  "averageDaily" : 3.8744800000000000E+003,
  "busiestDay" : {
    "day" : 7,
    "amount" : 5.4101800000000003E+003
  }
};
    const paymentData = {
  "methods" : {
    "QR Code" : 7.9267990000000005E+004,
    "Cash" : 3.6863160000000003E+004,
    "CN" : 1.0318000000000001E+002
  }
};
    const dailyData = {
  "dates" : [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30"
  ],
  "amounts" : [
    4.0828600000000010E+003,
    4.5434300000000021E+003,
    3.9912300000000009E+003,
    3.8565600000000004E+003,
    4.0448400000000015E+003,
    4.4441899999999987E+003,
    5.4101800000000048E+003,
    4.4380499999999984E+003,
    4.4890799999999999E+003,
    4.0717999999999997E+003,
    3.4666400000000012E+003,
    4.2753899999999967E+003,
    4.1321000000000004E+003,
    3.4486299999999987E+003,
    2.5943299999999995E+003,
    4.1697299999999996E+003,
    3.6982500000000009E+003,
    3.6003899999999994E+003,
    4.0190700000000002E+003,
    3.9975000000000000E+003,
    3.4115899999999992E+003,
    3.3174799999999991E+003,
    3.4987799999999997E+003,
    3.1890300000000002E+003,
    4.1370100000000002E+003,
    4.7023599999999988E+003,
    3.3618600000000001E+003,
    3.3305200000000009E+003,
    2.4827099999999991E+003,
    4.0287400000000011E+003
  ],
  "discounts" : [
    3.5951999999999992E+002,
    3.7968999999999977E+002,
    5.7460000000000048E+002,
    4.3553000000000003E+002,
    4.7015999999999985E+002,
    4.2337000000000000E+002,
    5.1165000000000009E+002,
    3.6898999999999995E+002,
    4.8089000000000027E+002,
    4.6239999999999998E+002,
    3.8165000000000020E+002,
    4.6750999999999993E+002,
    4.0565000000000009E+002,
    3.1626999999999992E+002,
    2.6333000000000010E+002,
    4.4516000000000025E+002,
    5.1757000000000028E+002,
    3.3571000000000009E+002,
    5.1019000000000034E+002,
    3.3541999999999990E+002,
    3.6445999999999981E+002,
    3.2414999999999992E+002,
    4.1931999999999994E+002,
    4.7898000000000008E+002,
    3.6678000000000009E+002,
    4.9615999999999997E+002,
    3.3858000000000004E+002,
    2.0885999999999996E+002,
    1.9315999999999994E+002,
    4.5703999999999985E+002
  ],
  "cash" : [
    1.0626800000000001E+003,
    1.4362800000000004E+003,
    1.2394800000000005E+003,
    1.1140400000000000E+003,
    1.0729900000000000E+003,
    1.2699000000000001E+003,
    1.8237099999999998E+003,
    1.2158599999999999E+003,
    1.1310800000000004E+003,
    1.3487800000000000E+003,
    1.2555299999999997E+003,
    1.5028000000000000E+003,
    1.2013200000000002E+003,
    8.1377999999999986E+002,
    8.0653000000000020E+002,
    1.2642100000000003E+003,
    1.1598599999999999E+003,
    1.1979200000000003E+003,
    1.4451199999999997E+003,
    1.2813199999999997E+003,
    1.1618600000000001E+003,
    1.0351500000000001E+003,
    1.6332299999999996E+003,
    9.1718000000000006E+002,
    8.3369000000000005E+002,
    1.8193500000000004E+003,
    1.2739399999999998E+003,
    1.2165500000000000E+003,
    7.9066999999999996E+002,
    1.5383500000000001E+003
  ],
  "nonCash" : [
    3.0201800000000012E+003,
    3.1071500000000010E+003,
    2.7517499999999991E+003,
    2.7425200000000000E+003,
    2.9718500000000013E+003,
    3.1742900000000004E+003,
    3.5864700000000003E+003,
    3.2221899999999987E+003,
    3.3580000000000000E+003,
    2.7230199999999995E+003,
    2.2111100000000001E+003,
    2.7725900000000006E+003,
    2.9307800000000011E+003,
    2.6348499999999999E+003,
    1.7878000000000000E+003,
    2.9055199999999986E+003,
    2.5383899999999985E+003,
    2.4024700000000003E+003,
    2.5739500000000003E+003,
    2.7161799999999998E+003,
    2.2497300000000005E+003,
    2.2823300000000004E+003,
    1.8655499999999995E+003,
    2.2718500000000008E+003,
    3.3033200000000011E+003,
    2.8830099999999989E+003,
    2.0879199999999996E+003,
    2.1139699999999993E+003,
    1.6920399999999995E+003,
    2.4903900000000008E+003
  ],
  "paymentMethods" : [
    "QR Code",
    "Cash",
    "CN"
  ],
  "dailyBreakdown" : [
    {
      "day" : 1,
      "total" : 4.0828600000000010E+003,
      "discounts" : 3.5951999999999992E+002,
      "cash" : 1.0626800000000001E+003,
      "nonCash" : 3.0201800000000012E+003,
      "payments" : {
        "QR Code" : 3.0201800000000012E+003,
        "Cash" : 1.0626800000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 2,
      "total" : 4.5434300000000021E+003,
      "discounts" : 3.7968999999999977E+002,
      "cash" : 1.4362800000000004E+003,
      "nonCash" : 3.1071500000000010E+003,
      "payments" : {
        "QR Code" : 3.1071500000000010E+003,
        "Cash" : 1.4362800000000004E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 3,
      "total" : 3.9912300000000009E+003,
      "discounts" : 5.7460000000000048E+002,
      "cash" : 1.2394800000000005E+003,
      "nonCash" : 2.7517499999999991E+003,
      "payments" : {
        "QR Code" : 2.7517499999999991E+003,
        "Cash" : 1.2394800000000005E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 4,
      "total" : 3.8565600000000004E+003,
      "discounts" : 4.3553000000000003E+002,
      "cash" : 1.1140400000000000E+003,
      "nonCash" : 2.7425200000000000E+003,
      "payments" : {
        "QR Code" : 2.7425200000000000E+003,
        "Cash" : 1.1140400000000000E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 5,
      "total" : 4.0448400000000015E+003,
      "discounts" : 4.7015999999999985E+002,
      "cash" : 1.0729900000000000E+003,
      "nonCash" : 2.9718500000000013E+003,
      "payments" : {
        "QR Code" : 2.9718500000000013E+003,
        "Cash" : 1.0729900000000000E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 6,
      "total" : 4.4441899999999987E+003,
      "discounts" : 4.2337000000000000E+002,
      "cash" : 1.2699000000000001E+003,
      "nonCash" : 3.1742900000000004E+003,
      "payments" : {
        "QR Code" : 3.1565600000000004E+003,
        "Cash" : 1.2699000000000001E+003,
        "CN" : 1.7730000000000000E+001
      }
    },
    {
      "day" : 7,
      "total" : 5.4101800000000048E+003,
      "discounts" : 5.1165000000000009E+002,
      "cash" : 1.8237099999999998E+003,
      "nonCash" : 3.5864700000000003E+003,
      "payments" : {
        "QR Code" : 3.5864700000000003E+003,
        "Cash" : 1.8237099999999998E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 8,
      "total" : 4.4380499999999984E+003,
      "discounts" : 3.6898999999999995E+002,
      "cash" : 1.2158599999999999E+003,
      "nonCash" : 3.2221899999999987E+003,
      "payments" : {
        "QR Code" : 3.2221899999999987E+003,
        "Cash" : 1.2158599999999999E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 9,
      "total" : 4.4890799999999999E+003,
      "discounts" : 4.8089000000000027E+002,
      "cash" : 1.1310800000000004E+003,
      "nonCash" : 3.3580000000000000E+003,
      "payments" : {
        "QR Code" : 3.3580000000000000E+003,
        "Cash" : 1.1310800000000004E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 10,
      "total" : 4.0717999999999997E+003,
      "discounts" : 4.6239999999999998E+002,
      "cash" : 1.3487800000000000E+003,
      "nonCash" : 2.7230199999999995E+003,
      "payments" : {
        "QR Code" : 2.7230199999999995E+003,
        "Cash" : 1.3487800000000000E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 11,
      "total" : 3.4666400000000012E+003,
      "discounts" : 3.8165000000000020E+002,
      "cash" : 1.2555299999999997E+003,
      "nonCash" : 2.2111100000000001E+003,
      "payments" : {
        "QR Code" : 2.2111100000000001E+003,
        "Cash" : 1.2555299999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 12,
      "total" : 4.2753899999999967E+003,
      "discounts" : 4.6750999999999993E+002,
      "cash" : 1.5028000000000000E+003,
      "nonCash" : 2.7725900000000006E+003,
      "payments" : {
        "QR Code" : 2.7725900000000006E+003,
        "Cash" : 1.5028000000000000E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 13,
      "total" : 4.1321000000000004E+003,
      "discounts" : 4.0565000000000009E+002,
      "cash" : 1.2013200000000002E+003,
      "nonCash" : 2.9307800000000011E+003,
      "payments" : {
        "QR Code" : 2.9175000000000009E+003,
        "Cash" : 1.2013200000000002E+003,
        "CN" : 1.3279999999999999E+001
      }
    },
    {
      "day" : 14,
      "total" : 3.4486299999999987E+003,
      "discounts" : 3.1626999999999992E+002,
      "cash" : 8.1377999999999986E+002,
      "nonCash" : 2.6348499999999999E+003,
      "payments" : {
        "QR Code" : 2.6348499999999999E+003,
        "Cash" : 8.1377999999999986E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 15,
      "total" : 2.5943299999999995E+003,
      "discounts" : 2.6333000000000010E+002,
      "cash" : 8.0653000000000020E+002,
      "nonCash" : 1.7878000000000000E+003,
      "payments" : {
        "QR Code" : 1.7878000000000000E+003,
        "Cash" : 8.0653000000000020E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 16,
      "total" : 4.1697299999999996E+003,
      "discounts" : 4.4516000000000025E+002,
      "cash" : 1.2642100000000003E+003,
      "nonCash" : 2.9055199999999986E+003,
      "payments" : {
        "QR Code" : 2.9055199999999986E+003,
        "Cash" : 1.2642100000000003E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 17,
      "total" : 3.6982500000000009E+003,
      "discounts" : 5.1757000000000028E+002,
      "cash" : 1.1598599999999999E+003,
      "nonCash" : 2.5383899999999985E+003,
      "payments" : {
        "QR Code" : 2.5151099999999988E+003,
        "Cash" : 1.1598599999999999E+003,
        "CN" : 2.3280000000000001E+001
      }
    },
    {
      "day" : 18,
      "total" : 3.6003899999999994E+003,
      "discounts" : 3.3571000000000009E+002,
      "cash" : 1.1979200000000003E+003,
      "nonCash" : 2.4024700000000003E+003,
      "payments" : {
        "QR Code" : 2.4024700000000003E+003,
        "Cash" : 1.1979200000000003E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 19,
      "total" : 4.0190700000000002E+003,
      "discounts" : 5.1019000000000034E+002,
      "cash" : 1.4451199999999997E+003,
      "nonCash" : 2.5739500000000003E+003,
      "payments" : {
        "QR Code" : 2.5593800000000001E+003,
        "Cash" : 1.4451199999999997E+003,
        "CN" : 1.4570000000000000E+001
      }
    },
    {
      "day" : 20,
      "total" : 3.9975000000000000E+003,
      "discounts" : 3.3541999999999990E+002,
      "cash" : 1.2813199999999997E+003,
      "nonCash" : 2.7161799999999998E+003,
      "payments" : {
        "QR Code" : 2.7161799999999998E+003,
        "Cash" : 1.2813199999999997E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 21,
      "total" : 3.4115899999999992E+003,
      "discounts" : 3.6445999999999981E+002,
      "cash" : 1.1618600000000001E+003,
      "nonCash" : 2.2497300000000005E+003,
      "payments" : {
        "QR Code" : 2.2497300000000005E+003,
        "Cash" : 1.1618600000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 22,
      "total" : 3.3174799999999991E+003,
      "discounts" : 3.2414999999999992E+002,
      "cash" : 1.0351500000000001E+003,
      "nonCash" : 2.2823300000000004E+003,
      "payments" : {
        "QR Code" : 2.2823300000000004E+003,
        "Cash" : 1.0351500000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 23,
      "total" : 3.4987799999999997E+003,
      "discounts" : 4.1931999999999994E+002,
      "cash" : 1.6332299999999996E+003,
      "nonCash" : 1.8655499999999995E+003,
      "payments" : {
        "QR Code" : 1.8655499999999995E+003,
        "Cash" : 1.6332299999999996E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 24,
      "total" : 3.1890300000000002E+003,
      "discounts" : 4.7898000000000008E+002,
      "cash" : 9.1718000000000006E+002,
      "nonCash" : 2.2718500000000008E+003,
      "payments" : {
        "QR Code" : 2.2718500000000008E+003,
        "Cash" : 9.1718000000000006E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 25,
      "total" : 4.1370100000000002E+003,
      "discounts" : 3.6678000000000009E+002,
      "cash" : 8.3369000000000005E+002,
      "nonCash" : 3.3033200000000011E+003,
      "payments" : {
        "QR Code" : 3.2797700000000009E+003,
        "Cash" : 8.3369000000000005E+002,
        "CN" : 2.3550000000000001E+001
      }
    },
    {
      "day" : 26,
      "total" : 4.7023599999999988E+003,
      "discounts" : 4.9615999999999997E+002,
      "cash" : 1.8193500000000004E+003,
      "nonCash" : 2.8830099999999989E+003,
      "payments" : {
        "QR Code" : 2.8802299999999987E+003,
        "Cash" : 1.8193500000000004E+003,
        "CN" : 2.7799999999999998E+000
      }
    },
    {
      "day" : 27,
      "total" : 3.3618600000000001E+003,
      "discounts" : 3.3858000000000004E+002,
      "cash" : 1.2739399999999998E+003,
      "nonCash" : 2.0879199999999996E+003,
      "payments" : {
        "QR Code" : 2.0879199999999996E+003,
        "Cash" : 1.2739399999999998E+003,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 28,
      "total" : 3.3305200000000009E+003,
      "discounts" : 2.0885999999999996E+002,
      "cash" : 1.2165500000000000E+003,
      "nonCash" : 2.1139699999999993E+003,
      "payments" : {
        "QR Code" : 2.1059799999999996E+003,
        "Cash" : 1.2165500000000000E+003,
        "CN" : 7.9900000000000002E+000
      }
    },
    {
      "day" : 29,
      "total" : 2.4827099999999991E+003,
      "discounts" : 1.9315999999999994E+002,
      "cash" : 7.9066999999999996E+002,
      "nonCash" : 1.6920399999999995E+003,
      "payments" : {
        "QR Code" : 1.6920399999999995E+003,
        "Cash" : 7.9066999999999996E+002,
        "CN" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 30,
      "total" : 4.0287400000000011E+003,
      "discounts" : 4.5703999999999985E+002,
      "cash" : 1.5383500000000001E+003,
      "nonCash" : 2.4903900000000008E+003,
      "payments" : {
        "QR Code" : 2.4903900000000008E+003,
        "Cash" : 1.5383500000000001E+003,
        "CN" : 0.0000000000000000E+000
      }
    }
  ]
};
    const hourlyData = {
  "hours" : [
    {
      "hour" : 0,
      "total" : 9.5542300000000123E+003,
      "count" : 406,
      "average" : 2.3532586206896582E+001
    },
    {
      "hour" : 1,
      "total" : 6.9264700000000057E+003,
      "count" : 298,
      "average" : 2.3243187919463107E+001
    },
    {
      "hour" : 2,
      "total" : 4.1475200000000004E+003,
      "count" : 204,
      "average" : 2.0330980392156864E+001
    },
    {
      "hour" : 3,
      "total" : 9.7368999999999994E+002,
      "count" : 73,
      "average" : 1.3338219178082191E+001
    },
    {
      "hour" : 4,
      "total" : 1.1830000000000000E+001,
      "count" : 1,
      "average" : 1.1830000000000000E+001
    },
    {
      "hour" : 5,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 6,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 7,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 8,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 9,
      "total" : 1.8124000000000001E+002,
      "count" : 8,
      "average" : 2.2655000000000001E+001
    },
    {
      "hour" : 10,
      "total" : 1.6293500000000010E+003,
      "count" : 71,
      "average" : 2.2948591549295788E+001
    },
    {
      "hour" : 11,
      "total" : 2.7154400000000001E+003,
      "count" : 122,
      "average" : 2.2257704918032786E+001
    },
    {
      "hour" : 12,
      "total" : 4.3092900000000018E+003,
      "count" : 172,
      "average" : 2.5054011627906988E+001
    },
    {
      "hour" : 13,
      "total" : 4.5741099999999988E+003,
      "count" : 203,
      "average" : 2.2532561576354674E+001
    },
    {
      "hour" : 14,
      "total" : 5.2128500000000031E+003,
      "count" : 245,
      "average" : 2.1276938775510217E+001
    },
    {
      "hour" : 15,
      "total" : 5.8046799999999985E+003,
      "count" : 284,
      "average" : 2.0439014084507036E+001
    },
    {
      "hour" : 16,
      "total" : 7.6904100000000017E+003,
      "count" : 349,
      "average" : 2.2035558739255020E+001
    },
    {
      "hour" : 17,
      "total" : 6.5531100000000015E+003,
      "count" : 315,
      "average" : 2.0803523809523814E+001
    },
    {
      "hour" : 18,
      "total" : 7.3573299999999990E+003,
      "count" : 315,
      "average" : 2.3356603174603173E+001
    },
    {
      "hour" : 19,
      "total" : 7.5689400000000051E+003,
      "count" : 274,
      "average" : 2.7623868613138704E+001
    },
    {
      "hour" : 20,
      "total" : 7.9318599999999979E+003,
      "count" : 269,
      "average" : 2.9486468401486981E+001
    },
    {
      "hour" : 21,
      "total" : 1.1498359999999999E+004,
      "count" : 378,
      "average" : 3.0418941798941795E+001
    },
    {
      "hour" : 22,
      "total" : 1.0977760000000007E+004,
      "count" : 411,
      "average" : 2.6709878345498801E+001
    },
    {
      "hour" : 23,
      "total" : 1.0615860000000001E+004,
      "count" : 425,
      "average" : 2.4978494117647060E+001
    }
  ]
};

    // Toggle functionality
    function toggleView(section, view) {
      const contentElement = document.getElementById(section + '-content');
      const buttons = contentElement.parentElement.querySelectorAll('.toggle-button');

      // Update button states
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      // Update content view
      contentElement.className = 'section-content ' + view + '-view';

      // Save preference
      localStorage.setItem(section + '-view', view);
    }

    // Load saved preferences
    function loadViewPreferences() {
      const dailyView = localStorage.getItem('daily-view') || 'table';
      const paymentView = localStorage.getItem('payment-view') || 'table';

      // Set daily section
      const dailyContent = document.getElementById('daily-content');
      const dailyButtons = dailyContent.parentElement.querySelectorAll('.toggle-button');
      dailyContent.className = 'section-content ' + dailyView + '-view';
      dailyButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(dailyView));
      });

      // Set payment section
      const paymentContent = document.getElementById('payment-content');
      const paymentButtons = paymentContent.parentElement.querySelectorAll('.toggle-button');
      paymentContent.className = 'section-content ' + paymentView + '-view';
      paymentButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(paymentView));
      });
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2
      }).format(amount);
    }
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Load view preferences first
      loadViewPreferences();

      try {
        // Daily Sales Chart
        const dailyCtx = document.getElementById('dailySalesChart');
        if (dailyCtx) {
          // Prepare data for multiple series
          const datasets = [];

          // Check if we have breakdown data
          if (dailyData.cash && dailyData.nonCash && dailyData.discounts) {
            // Show 4 series: Total, Cash, Non-Cash, Discounts
            datasets.push({
              label: 'Total Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Cash',
              data: dailyData.cash || [],
              backgroundColor: 'rgba(75, 192, 192, 0.8)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Non-Cash',
              data: dailyData.nonCash || [],
              backgroundColor: 'rgba(255, 159, 64, 0.8)',
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Discounts',
              data: dailyData.discounts || [],
              backgroundColor: 'rgba(255, 99, 132, 0.8)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2
            });
          } else {
            // Fallback to single series
            datasets.push({
              label: 'Daily Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
          }

          new Chart(dailyCtx, {
            type: 'bar',
            data: {
              labels: dailyData.dates || [],
              datasets: datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return formatCurrency(context.raw);
                    }
                  }
                },
                legend: {
                  display: datasets.length > 1
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Daily Sales Table
        const dailyTableBody = document.getElementById('dailyTableBody');
        const dailyTableHeader = document.getElementById('dailyTableHeader');
        if (dailyTableBody && dailyTableHeader && dailyData.dates && dailyData.amounts) {
          const paymentMethods = dailyData.paymentMethods || [];
          const dailyBreakdown = dailyData.dailyBreakdown || [];

          // Clear existing content
          dailyTableBody.innerHTML = '';
          dailyTableHeader.innerHTML = '';

          // Create header row
          const headerRow = document.createElement('tr');
          headerRow.innerHTML = `
            <th>Day</th>
            <th>Total Sales</th>
            <th>Discounts</th>
            <th>% of Month</th>
            ${paymentMethods.map(method => `<th>${method}</th>`).join('')}
          `;
          dailyTableHeader.appendChild(headerRow);

          const monthTotal = dailyData.amounts.reduce((sum, amount) => sum + amount, 0);

          // Add rows for each day
          dailyData.dates.forEach((date, index) => {
            const amount = dailyData.amounts[index] || 0;
            const discount = (dailyData.discounts && dailyData.discounts[index]) || 0;
            const percentage = monthTotal > 0 ? (amount / monthTotal * 100).toFixed(1) : '0.0';
            const dayNumber = index + 1; // Convert to 1-based day

            // Find payment breakdown for this day
            const dayBreakdown = dailyBreakdown.find(db => db.day === dayNumber);
            const payments = dayBreakdown ? dayBreakdown.payments : {};

            const displayAmount = amount > 0 ? formatCurrency(amount) : '';
            const displayDiscount = discount > 0 ? formatCurrency(discount) : '';
            const displayPercentage = amount > 0 ? percentage + '%' : '';

            // Generate payment method cells
            let paymentCells = '';
            paymentMethods.forEach(method => {
              const methodAmount = payments[method] || 0;
              const displayMethodAmount = methodAmount > 0 ? formatCurrency(methodAmount) : '';
              paymentCells += `<td class="payment-amount">${displayMethodAmount}</td>`;
            });

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${date}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-amount">${displayDiscount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
              ${paymentCells}
            `;
            dailyTableBody.appendChild(row);
          });

          // Add total row
          if (monthTotal > 0) {
            // Calculate totals for each column
            const totalDiscounts = (dailyData.discounts || []).reduce((sum, amount) => sum + amount, 0);

            // Calculate payment method totals
            let paymentTotalCells = '';
            paymentMethods.forEach(method => {
              let methodTotal = 0;
              dailyBreakdown.forEach(db => {
                methodTotal += db.payments[method] || 0;
              });
              paymentTotalCells += `<td class="payment-amount"><strong>${formatCurrency(methodTotal)}</strong></td>`;
            });

            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(monthTotal)}</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalDiscounts)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
              ${paymentTotalCells}
            `;
            dailyTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing daily sales chart:', error);
      }

      // Payment Methods Chart
      try {
        const paymentCtx = document.getElementById('paymentMethodsChart');
        if (paymentCtx) {
          new Chart(paymentCtx, {
            type: 'pie',
            data: {
              labels: Object.keys(paymentData.methods || {}),
              datasets: [{
                data: Object.values(paymentData.methods || {}),
                backgroundColor: [
                  '#3498db', '#2ecc71', '#e74c3c', '#f1c40f',
                  '#9b59b6', '#1abc9c', '#e67e22', '#74c0fc'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Payment Methods Distribution',
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = Math.round((value / total) * 100);
                      return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Payment Methods Table
        const paymentTableBody = document.getElementById('paymentTableBody');
        if (paymentTableBody && paymentData.methods) {
          const methods = paymentData.methods;
          const methodNames = Object.keys(methods);
          const methodAmounts = Object.values(methods);

          // Calculate total for percentages
          const totalAmount = methodAmounts.reduce((sum, amount) => sum + amount, 0);

          // Sort methods by amount (descending)
          const sortedMethods = methodNames
            .map(name => ({ name, amount: methods[name] }))
            .sort((a, b) => b.amount - a.amount);

          // Clear existing content
          paymentTableBody.innerHTML = '';

          // Add rows for each payment method
          sortedMethods.forEach(method => {
            const percentage = totalAmount > 0 ? (method.amount / totalAmount * 100).toFixed(1) : '0.0';
            const displayAmount = method.amount > 0 ? formatCurrency(method.amount) : '';
            const displayPercentage = method.amount > 0 ? percentage + '%' : '';

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${method.name}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
            `;
            paymentTableBody.appendChild(row);
          });

          // Add total row
          if (totalAmount > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalAmount)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
            `;
            paymentTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing payment methods chart:', error);
      }

      // Update summary content
      try {
        const summaryContent = document.getElementById('summary-content');
        if (summaryContent && monthlyData) {
          summaryContent.innerHTML =
            '<div class="summary-item">' +
            'Total Sales: <span class="summary-value">' + formatCurrency(monthlyData.totalSales || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Average Daily Sales: <span class="summary-value">' + formatCurrency(monthlyData.averageDaily || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Busiest Day: <span class="summary-value">' +
            (monthlyData.busiestDay ? monthlyData.busiestDay.day + ' (' + formatCurrency(monthlyData.busiestDay.amount) + ')' : 'N/A') +
            '</span></div>';
        }
      } catch (error) {
        console.error('Error updating summary content:', error);
      }

      // Hourly Distribution Chart
      try {
        const hourlyCtx = document.getElementById('hourlyDistributionChart');
        if (hourlyCtx) {
          new Chart(hourlyCtx.getContext('2d'), {
            type: 'line',
            data: {
              labels: Array.from({length: 24}, (_, i) => `${i}:00`),
              datasets: [{
                label: 'Average Sales',
                data: hourlyData.hours ? hourlyData.hours.map(hour => hour.average) : [],
                borderColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return `Avg: ${formatCurrency(context.raw)}`;
                    }
                  }
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }
      } catch (error) {
        console.error('Error initializing hourly distribution chart:', error);
      }
    }); // Close DOMContentLoaded
  </script>
</body>
</html>
