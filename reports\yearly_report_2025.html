<!DOCTYPE html>
<html>
<head>
  <title>Yearly Report - 2025</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .chart-container {
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 500px;
      width: 100%;
    }
    
    @media (max-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
    .summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 1.1em;
    }
    .summary-value {
      font-weight: bold;
      color: #2c3e50;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-section,
    .monthly-section {
      display: flex;
      gap: 30px;
      margin: 40px 0;
      align-items: flex-start;
    }
    .payment-chart,
    .monthly-chart {
      flex: 1;
      padding: 20px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 600px;
    }
    .payment-table-container,
    .monthly-table-container {
      flex: 1;
      padding: 20px;
      background: #f9f9f9;
      border-radius: 8px;
    }
    .monthly-table-container {
      overflow-x: auto; /* Allow horizontal scrolling for wide tables */
    }
    .payment-table,
    .monthly-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .monthly-table {
      font-size: 0.9em; /* Smaller font for more columns */
    }
    .payment-table th,
    .payment-table td,
    .monthly-table th,
    .monthly-table td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #eee;
    }
    .payment-table th,
    .monthly-table th {
      background: #3498db;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }
    .monthly-table th:nth-child(n+2) {
      text-align: right; /* Right align amount column headers */
    }
    .payment-table tr:hover,
    .monthly-table tr:hover {
      background: #f8f9fa;
    }
    .payment-table tr:last-child td,
    .monthly-table tr:last-child td {
      border-bottom: none;
    }
    .payment-amount {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }
    .payment-percentage {
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-method-name {
      font-weight: 500;
      color: #34495e;
    }
    .table-total {
      background: #ecf0f1 !important;
      font-weight: 600;
      border-top: 2px solid #3498db;
    }
    @media (max-width: 768px) {
      .payment-section,
      .monthly-section {
        flex-direction: column;
        gap: 20px;
      }
      .payment-chart,
      .monthly-chart {
        height: 400px;
      }
    }

    /* Toggle Controls */
    .toggle-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }

    .toggle-control {
      display: inline-flex;
      background: #ecf0f1;
      border-radius: 25px;
      padding: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .toggle-button {
      padding: 8px 20px;
      border: none;
      background: transparent;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      color: #7f8c8d;
    }

    .toggle-button.active {
      background: #3498db;
      color: white;
      box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
    }

    .toggle-button:hover:not(.active) {
      color: #2c3e50;
    }

    /* Hide/Show functionality */
    .chart-view .table-container,
    .table-view .chart-container-section {
      display: none;
    }

    .chart-view .chart-container-section,
    .table-view .table-container {
      display: block;
      flex: 1;
    }

    /* Full width when only one view is shown */
    .section-content.chart-view .chart-container-section,
    .section-content.table-view .table-container {
      width: 100%;
      max-width: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Yearly Sales Report - 2025</h1>
      <p>Generated on 2025-08-17 00:16:23</p>
    </div>
    
    <div class="summary">
      <h2>Summary</h2>
      <div id="summary-content">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>
    
    <div class="monthly-section">
      <div class="toggle-container">
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('monthly', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('monthly', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="monthly-content">
        <div class="chart-container-section monthly-chart">
          <h2>Monthly Sales - 2025</h2>
          <div style="position: relative; height: calc(100% - 40px); width: 100%;">
            <canvas id="monthlySalesChart"></canvas>
          </div>
        </div>

        <div class="table-container monthly-table-container">
          <h2>Monthly Sales Breakdown</h2>
          <table class="monthly-table" id="monthlySalesTable">
            <thead id="monthlyTableHeader">
              <!-- Will be populated by JavaScript -->
            </thead>
            <tbody id="monthlyTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <div class="payment-section">
      <div class="toggle-container">
        <div class="toggle-control">
          <button class="toggle-button active" onclick="toggleView('payment', 'table')">Tables</button>
          <button class="toggle-button" onclick="toggleView('payment', 'chart')">Charts</button>
        </div>
      </div>

      <div class="section-content table-view" id="payment-content">
        <div class="chart-container-section payment-chart">
          <h2>Payment Methods - 2025</h2>
          <div style="position: relative; height: calc(100% - 40px); width: 100%;">
            <canvas id="paymentMethodsChart"></canvas>
          </div>
        </div>

        <div class="table-container payment-table-container">
          <h2>Payment Methods Breakdown</h2>
          <table class="payment-table" id="paymentMethodsTable">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Percentage</th>
              </tr>
            </thead>
            <tbody id="paymentTableBody">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>SnP Report Generator | Generated by Asia Club Management System</p>
    </div>
  </div>

  <script>
    // Parse the JSON data passed from Pascal
    const monthlyData = JSON.parse('{\n  \"year\": 2025,\n  \"monthlyTotals\": [\n    0.00\n    ,0.00\n    ,0.00\n    ,0.00\n    ,56536.85\n    ,116234.33\n    ,98758.25\n    ,16463.66\n    ,0.00\n    ,0.00\n    ,0.00\n    ,0.00\n  ],\n  \"monthlyDiscounts\": [\n    0.00\n    ,0.00\n    ,0.00\n    ,0.00\n    ,6270.14\n    ,12092.75\n    ,11537.59\n    ,1721.35\n    ,0.00\n    ,0.00\n    ,0.00\n    ,0.00\n  ],\n  \"monthlyPaymentBreakdown\": [\n    {\n      \"month\": 1,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 2,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 3,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 4,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 5,\n      \"payments\": {\n        \"Cash\": 20637.63\n        ,\"QR Code\": 35838.25\n        ,\"CN\": 59.72\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 6,\n      \"payments\": {\n        \"Cash\": 36862.41\n        ,\"QR Code\": 79266.04\n        ,\"CN\": 103.18\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 7,\n      \"payments\": {\n        \"Cash\": 34407.68\n        ,\"QR Code\": 64045.71\n        ,\"CN\": 302.50\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 8,\n      \"payments\": {\n        \"Cash\": 8921.91\n        ,\"QR Code\": 3717.97\n        ,\"CN\": 0.00\n        ,\"QR\": 3517.99\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 305.38\n      }\n    }\n    ,{\n      \"month\": 9,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 10,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 11,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n    ,{\n      \"month\": 12,\n      \"payments\": {\n        \"Cash\": 0.00\n        ,\"QR Code\": 0.00\n        ,\"CN\": 0.00\n        ,\"QR\": 0.00\n        ,\"CN-M\": 0.00\n        ,\"CNS-C\": 0.00\n      }\n    }\n  ],\n  \"paymentMethods\": [\n    \"Cash\"\n    ,\"QR Code\"\n    ,\"CN\"\n    ,\"QR\"\n    ,\"CN-M\"\n    ,\"CNS-C\"\n  ],\n  \"monthlyCashBreakdown\": [\n    {\n      \"month\": 1,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 2,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 3,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 4,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 5,\n      \"total\": 56536.85,\n      \"cash\": 20637.63,\n      \"nonCash\": 35897.97\n    }\n    ,{\n      \"month\": 6,\n      \"total\": 116234.33,\n      \"cash\": 36862.41,\n      \"nonCash\": 79369.22\n    }\n    ,{\n      \"month\": 7,\n      \"total\": 98758.25,\n      \"cash\": 34407.68,\n      \"nonCash\": 64348.21\n    }\n    ,{\n      \"month\": 8,\n      \"total\": 16463.66,\n      \"cash\": 8921.91,\n      \"nonCash\": 7541.34\n    }\n    ,{\n      \"month\": 9,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 10,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 11,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n    ,{\n      \"month\": 12,\n      \"total\": 0.00,\n      \"cash\": 0.00,\n      \"nonCash\": 0.00\n    }\n  ],\n  \"yearlyTotal\": 287993.09,\n  \"yearlyDiscounts\": 31621.83,\n  \"averageMonthly\": 0.00,\n  \"bestMonth\": {\n    \"month\": 6,\n    \"amount\": 116234.33\n  }\n}\n');
    const paymentData = JSON.parse('{\n  \"methods\" : {\n    \"Cash\" : 1.0083187000000000E+005,\n    \"QR Code\" : 1.8287232000000001E+005,\n    \"CN\" : 4.6539999999999998E+002,\n    \"QR\" : 3.5180999999999999E+003,\n    \"CN-M\" : 0.0000000000000000E+000,\n    \"CNS-C\" : 3.0539999999999998E+002\n  }\n}');

    // Toggle functionality
    function toggleView(section, view) {
      const contentElement = document.getElementById(section + '-content');
      const buttons = contentElement.parentElement.querySelectorAll('.toggle-button');

      // Update button states
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      // Update content view
      contentElement.className = 'section-content ' + view + '-view';

      // Save preference
      localStorage.setItem(section + '-view', view);
    }

    // Load saved preferences
    function loadViewPreferences() {
      const monthlyView = localStorage.getItem('monthly-view') || 'table';
      const paymentView = localStorage.getItem('payment-view') || 'table';

      // Set monthly section
      const monthlyContent = document.getElementById('monthly-content');
      const monthlyButtons = monthlyContent.parentElement.querySelectorAll('.toggle-button');
      monthlyContent.className = 'section-content ' + monthlyView + '-view';
      monthlyButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(monthlyView));
      });

      // Set payment section
      const paymentContent = document.getElementById('payment-content');
      const paymentButtons = paymentContent.parentElement.querySelectorAll('.toggle-button');
      paymentContent.className = 'section-content ' + paymentView + '-view';
      paymentButtons.forEach(btn => {
        btn.classList.toggle('active', btn.textContent.toLowerCase().includes(paymentView));
      });
    }
    
    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2
      }).format(amount);
    }
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Load view preferences first
      loadViewPreferences();

      try {
        // Monthly Sales Chart
        const monthlyCtx = document.getElementById('monthlySalesChart');
        if (monthlyCtx) {
          // Prepare cash/non-cash data if available
          const cashBreakdown = monthlyData.monthlyCashBreakdown || [];
          const totalData = monthlyData.monthlyTotals;
          const cashData = cashBreakdown.map(item => item.cash || 0);
          const nonCashData = cashBreakdown.map(item => item.nonCash || 0);

          // Create datasets based on available data
          const datasets = [];

          if (cashBreakdown.length > 0) {
            // Show 4 series: Total, Cash, Non-Cash, Discounts
            const discountData = monthlyData.monthlyDiscounts || [];

            datasets.push({
              label: 'Total Sales',
              data: totalData,
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Cash',
              data: cashData,
              backgroundColor: 'rgba(75, 192, 192, 0.8)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Non-Cash',
              data: nonCashData,
              backgroundColor: 'rgba(255, 159, 64, 0.8)',
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Discounts',
              data: discountData,
              backgroundColor: 'rgba(255, 99, 132, 0.8)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2
            });
          } else {
            // Check if we have discount data even without cash breakdown
            const discountData = monthlyData.monthlyDiscounts || [];
            const hasDiscountData = discountData.some(amount => amount > 0);

            if (hasDiscountData) {
              // Show 2 series: Total Sales and Discounts
              datasets.push({
                label: 'Total Sales',
                data: totalData,
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2
              });
              datasets.push({
                label: 'Discounts',
                data: discountData,
                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 2
              });
            } else {
              // Show single series with same color for all bars
              datasets.push({
                label: 'Monthly Sales',
                data: totalData,
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2
              });
            }
          }

          new Chart(monthlyCtx, {
            type: 'bar',
            data: {
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
              datasets: datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Monthly Sales for ' + monthlyData.year,
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return formatCurrency(context.raw);
                    }
                  }
                },
                legend: {
                  display: datasets.length > 1
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }
        
        // Payment Methods Chart
        const paymentCtx = document.getElementById('paymentMethodsChart');
        if (paymentCtx) {
          new Chart(paymentCtx, {
            type: 'pie',
            data: {
              labels: Object.keys(paymentData.methods || {}),
              datasets: [{
                data: Object.values(paymentData.methods || {}),
                backgroundColor: [
                  '#3498db', '#2ecc71', '#e74c3c', '#f1c40f', 
                  '#9b59b6', '#1abc9c', '#e67e22', '#74c0fc'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Payment Methods Distribution',
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = Math.round((value / total) * 100);
                      return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Monthly Sales Table
        const monthlyTableBody = document.getElementById('monthlyTableBody');
        const monthlyTableHeader = document.getElementById('monthlyTableHeader');
        if (monthlyTableBody && monthlyTableHeader && monthlyData.monthlyTotals) {
          const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                            'July', 'August', 'September', 'October', 'November', 'December'];
          const monthlyTotals = monthlyData.monthlyTotals;
          const monthlyDiscounts = monthlyData.monthlyDiscounts || [];
          const yearlyTotal = monthlyData.yearlyTotal || monthlyTotals.reduce((sum, amount) => sum + amount, 0);
          const yearlyDiscounts = monthlyData.yearlyDiscounts || monthlyDiscounts.reduce((sum, amount) => sum + amount, 0);
          const paymentMethods = monthlyData.paymentMethods || [];
          const monthlyPaymentBreakdown = monthlyData.monthlyPaymentBreakdown || [];

          // Clear existing content
          monthlyTableBody.innerHTML = '';
          monthlyTableHeader.innerHTML = '';

          // Create header row
          const headerRow = document.createElement('tr');
          headerRow.innerHTML = `
            <th>Month</th>
            <th>Total Sales</th>
            <th>Discounts</th>
            <th>% of Year</th>
            ${paymentMethods.map(method => `<th>${method}</th>`).join('')}
          `;
          monthlyTableHeader.appendChild(headerRow);

          // Add rows for each month
          monthlyTotals.forEach((amount, index) => {
            const percentage = yearlyTotal > 0 ? (amount / yearlyTotal * 100).toFixed(1) : '0.0';
            const monthIndex = index + 1; // Convert to 1-based month
            const discountAmount = monthlyDiscounts[index] || 0;

            // Find payment breakdown for this month
            const monthBreakdown = monthlyPaymentBreakdown.find(mb => mb.month === monthIndex);
            const payments = monthBreakdown ? monthBreakdown.payments : {};

            const row = document.createElement('tr');
            let paymentCells = '';
            paymentMethods.forEach(method => {
              const methodAmount = payments[method] || 0;
              const displayAmount = methodAmount > 0 ? formatCurrency(methodAmount) : '';
              paymentCells += `<td class="payment-amount">${displayAmount}</td>`;
            });

            const displayAmount = amount > 0 ? formatCurrency(amount) : '';
            const displayDiscount = discountAmount > 0 ? formatCurrency(discountAmount) : '';
            const displayPercentage = amount > 0 ? percentage + '%' : '';

            row.innerHTML = `
              <td class="payment-method-name">${monthNames[index]}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-amount">${displayDiscount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
              ${paymentCells}
            `;
            monthlyTableBody.appendChild(row);
          });

          // Add total row
          if (yearlyTotal > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';

            // Calculate payment method totals
            let paymentTotalCells = '';
            paymentMethods.forEach(method => {
              let methodTotal = 0;
              monthlyPaymentBreakdown.forEach(mb => {
                methodTotal += mb.payments[method] || 0;
              });
              paymentTotalCells += `<td class="payment-amount"><strong>${formatCurrency(methodTotal)}</strong></td>`;
            });

            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(yearlyTotal)}</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(yearlyDiscounts)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
              ${paymentTotalCells}
            `;
            monthlyTableBody.appendChild(totalRow);
          }
        }

        // Populate Payment Methods Table
        const paymentTableBody = document.getElementById('paymentTableBody');
        if (paymentTableBody && paymentData.methods) {
          const methods = paymentData.methods;
          const methodNames = Object.keys(methods);
          const methodAmounts = Object.values(methods);

          // Calculate total for percentages
          const totalAmount = methodAmounts.reduce((sum, amount) => sum + amount, 0);

          // Sort methods by amount (descending)
          const sortedMethods = methodNames
            .map(name => ({ name, amount: methods[name] }))
            .sort((a, b) => b.amount - a.amount);

          // Clear existing content
          paymentTableBody.innerHTML = '';

          // Add rows for each payment method
          sortedMethods.forEach(method => {
            const percentage = totalAmount > 0 ? (method.amount / totalAmount * 100).toFixed(1) : '0.0';
            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${method.name}</td>
              <td class="payment-amount">${formatCurrency(method.amount)}</td>
              <td class="payment-percentage">${percentage}%</td>
            `;
            paymentTableBody.appendChild(row);
          });

          // Add total row
          if (totalAmount > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalAmount)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
            `;
            paymentTableBody.appendChild(totalRow);
          }
        }

        // Update summary content
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                          'July', 'August', 'September', 'October', 'November', 'December'];
        const bestMonthName = monthNames[monthlyData.bestMonth.month - 1] || 'N/A';
        
        const summaryContent = document.getElementById('summary-content');
        if (summaryContent) {
          summaryContent.innerHTML =
            '<div class="summary-item">' +
            'Total Sales: <span class="summary-value">' + formatCurrency(monthlyData.yearlyTotal) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Average Monthly Sales: <span class="summary-value">' + formatCurrency(monthlyData.averageMonthly) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Best Month: <span class="summary-value">' + bestMonthName + ' (' + formatCurrency(monthlyData.bestMonth.amount) + ')</span>' +
            '</div>';
        }
      } catch (error) {
        console.error('Error initializing charts:', error);
      }
    });
  </script>
</body>
</html>
