program ReportGenerator;

{$mode objfpc}{$H+}

uses
  Classes, SysUtils, IBConnection, SQLDB, DateUtils, uDB, uReports;

const
  CONFIG_FILE = 'config.ini';

type
  TReportGenerator = class
  private
    FSnpDB: TSnpDatabase;
    FReport: TSnpReport;
    FOutputDir: String;
    procedure InitializeDatabase;
    procedure LoadConfiguration;
    procedure Log(const Message: String);
  public
    constructor Create;
    destructor Destroy; override;
    procedure Execute;
    procedure GenerateHTMLReport(const ReportDate: String);
    property OutputDir: String read FOutputDir write FOutputDir;
  end;

constructor TReportGenerator.Create;
begin
  inherited Create;
  LoadConfiguration;
  InitializeDatabase;
end;

destructor TReportGenerator.Destroy;
begin
  if Assigned(FReport) then
    FReport.Free;
  if Assigned(FSnpDB) then
    FSnpDB.Free;
  inherited Destroy;
end;

procedure TReportGenerator.LoadConfiguration;
var
  ConfigFile: TStringList;
begin
  ConfigFile := TStringList.Create;
  try
    if FileExists(CONFIG_FILE) then
    begin
      ConfigFile.LoadFromFile(CONFIG_FILE);
      FOutputDir := ConfigFile.Values['OutputDirectory'];
    end
    else
    begin
      FOutputDir := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0))) + 'reports';
      ConfigFile.Add('OutputDirectory=' + FOutputDir);
      ConfigFile.SaveToFile(CONFIG_FILE);
    end;
  finally
    ConfigFile.Free;
  end;
  
  if not DirectoryExists(FOutputDir) then
    ForceDirectories(FOutputDir);
end;

procedure TReportGenerator.InitializeDatabase;
begin
  FSnpDB := TSnpDatabase.Create;
  FSnpDB.Connect;
  FReport := TSnpReport.Create(FSnpDB);
end;

procedure TReportGenerator.Execute;
var
  Year, Month, Day: Word;
  ReportDate: TDateTime;
begin
  ReportDate := Now;
  DecodeDate(ReportDate, Year, Month, Day);
  Log(Format('Starting report generation for %d-%.2d', [Year, Month]));
  
  try
    GenerateHTMLReport(Format('%.4d-%.2d', [Year, Month]));
    Log('Report generated successfully');
  except
    on E: Exception do
      Log('Error generating report: ' + E.Message);
  end;
end;

procedure TReportGenerator.GenerateHTMLReport(const ReportDate: String);
var
  Year, Month: Integer;
  DateParts: TStringArray;
begin
  // Parse the ReportDate (format: 'yyyy-mm')
  DateParts := ReportDate.Split(['-']);
  if Length(DateParts) >= 2 then
  begin
    Year := StrToIntDef(DateParts[0], 0);
    Month := StrToIntDef(DateParts[1], 0);
    
    if (Year > 0) and (Month > 0) then
    begin
      // Generate the monthly report using TSnpReport
      FReport.GenerateMonthlyReport(Year, Month, FOutputDir);
      Log(Format('Generated monthly report for %d-%.2d in %s', 
        [Year, Month, FOutputDir]));
    end
    else
    begin
      Log(Format('Invalid date format: %s', [ReportDate]));
    end;
  end
  else
  begin
    Log(Format('Invalid date format: %s', [ReportDate]));
  end;
end;
procedure TReportGenerator.Log(const Message: String);
var
  LogFile: TextFile;
  LogPath: String;
begin
  Writeln(Message);
  //LogPath := IncludeTrailingPathDelimiter(FOutputDir) + 'report_generator.log';
  //AssignFile(LogFile, LogPath);
  //try
  //  if FileExists(LogPath) then
  //    Append(LogFile)
  //  else
  //    Rewrite(LogFile);
  //  WriteLn(LogFile, FormatDateTime('yyyy-mm-dd hh:nn:ss', Now) + ' - ' + Message);
  //finally
  //  CloseFile(LogFile);
  //end;
end;

var
  ReportGen: TReportGenerator;
begin
  ReportGen := TReportGenerator.Create;
  try
    ReportGen.Execute;
  finally
    ReportGen.Free;
  end;
end.
