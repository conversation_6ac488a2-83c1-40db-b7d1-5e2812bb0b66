<!DOCTYPE html>
<html>
<head>
  <title>Monthly Report - 8/2025</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .chart-container {
      margin: 20px 0;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 500px;
      width: 100%;
    }

    @media (max-width: 768px) {
      .chart-container {
        height: 400px;
      }
    }
    .daily-section,
    .payment-section {
      display: flex;
      gap: 30px;
      margin: 20px 0;
      align-items: flex-start;
    }
    .daily-chart,
    .payment-chart {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      position: relative;
      height: 600px;
    }
    .daily-table-container,
    .payment-table-container {
      flex: 1;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 8px;
      overflow-x: auto;
    }
    .daily-table-container {
      overflow-x: auto; /* Allow horizontal scrolling for wide tables */
    }
    .daily-table,
    .payment-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-size: 0.9em;
    }
    .daily-table {
      font-size: 0.8em; /* Smaller font for more columns */
    }
    .daily-table th,
    .daily-table td,
    .payment-table th,
    .payment-table td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #eee;
    }
    .daily-table th,
    .payment-table th {
      background: #3498db;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }
    .daily-table th:nth-child(n+2),
    .payment-table th:nth-child(n+2) {
      text-align: right;
    }
    .daily-table tr:hover,
    .payment-table tr:hover {
      background: #f8f9fa;
    }
    .daily-table tr:last-child td,
    .payment-table tr:last-child td {
      border-bottom: none;
    }
    .payment-amount {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }
    .payment-percentage {
      color: #7f8c8d;
      font-size: 0.9em;
    }
    .payment-method-name {
      font-weight: 500;
      color: #34495e;
    }
    .table-total {
      background: #ecf0f1 !important;
      font-weight: 600;
      border-top: 2px solid #3498db;
    }
    .summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
    .summary-item {
      margin: 10px 0;
      font-size: 1.1em;
    }
    .summary-value {
      font-weight: bold;
      color: #2c3e50;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #7f8c8d;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Monthly Sales Report - 8/2025</h1>
      <p>Generated on 2025-08-16 16:52:07</p>
    </div>

    <div class="summary">
      <h2>Summary</h2>
      <div id="summary-content">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>

    <div class="daily-section">
      <div class="daily-chart">
        <h2>Daily Sales - 8/2025</h2>
        <div style="position: relative; height: calc(100% - 40px); width: 100%;">
          <canvas id="dailySalesChart"></canvas>
        </div>
      </div>

      <div class="daily-table-container">
        <h2>Daily Sales Breakdown</h2>
        <table class="daily-table" id="dailySalesTable">
          <thead id="dailyTableHeader">
            <!-- Will be populated by JavaScript -->
          </thead>
          <tbody id="dailyTableBody">
            <!-- Will be populated by JavaScript -->
          </tbody>
        </table>
      </div>
    </div>

    <div class="payment-section">
      <div class="payment-chart">
        <h2>Payment Methods - 8/2025</h2>
        <div style="position: relative; height: calc(100% - 40px); width: 100%;">
          <canvas id="paymentMethodsChart"></canvas>
        </div>
      </div>

      <div class="payment-table-container">
        <h2>Payment Methods Breakdown</h2>
        <table class="payment-table" id="paymentMethodsTable">
          <thead>
            <tr>
              <th>Payment Method</th>
              <th>Amount</th>
              <th>Percentage</th>
            </tr>
          </thead>
          <tbody id="paymentTableBody">
            <!-- Will be populated by JavaScript -->
          </tbody>
        </table>
      </div>
    </div>

    <div class="chart-container">
      <h2>Hourly Distribution - 8/2025</h2>
      <div style="position: relative; height: calc(100% - 40px); width: 100%;">
        <canvas id="hourlyDistributionChart"></canvas>
      </div>
    </div>

    <div class="footer">
      <p>SnP Report Generator | Generated by Asia Club Management System</p>
    </div>
  </div>

  <script>
    // Parse the JSON data passed from Pascal
    const monthlyData = {
  "year" : 2025,
  "month" : 8,
  "totalSales" : 1.6463660000000000E+004,
  "averageDaily" : 5.3109000000000003E+002,
  "busiestDay" : {
    "day" : 3,
    "amount" : 5.7949899999999998E+003
  }
};
    const paymentData = {
  "methods" : {
    "QR Code" : 3.7180500000000002E+003,
    "Cash" : 8.9221100000000006E+003,
    "QR" : 3.5180999999999999E+003,
    "CN-M" : 0.0000000000000000E+000,
    "CNS-C" : 3.0539999999999998E+002
  }
};
    const dailyData = {
  "dates" : [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31"
  ],
  "amounts" : [
    3.3024400000000001E+003,
    3.3415299999999979E+003,
    5.7949899999999998E+003,
    3.3030999999999999E+003,
    7.2080000000000007E+002,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    8.0000000000000004E-001,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000
  ],
  "discounts" : [
    2.7880999999999995E+002,
    4.2488999999999993E+002,
    5.7592999999999995E+002,
    3.2791999999999985E+002,
    1.1355999999999999E+002,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    2.3999999999999999E-001,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000
  ],
  "cash" : [
    1.6408699999999997E+003,
    1.7078999999999996E+003,
    4.0997399999999989E+003,
    1.2570999999999999E+003,
    2.1569999999999999E+002,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    8.0000000000000004E-001,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000
  ],
  "nonCash" : [
    1.6615700000000008E+003,
    1.6336299999999999E+003,
    1.6952500000000002E+003,
    2.0460000000000005E+003,
    5.0510000000000002E+002,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000,
    0.0000000000000000E+000
  ],
  "paymentMethods" : [
    "QR Code",
    "Cash",
    "QR",
    "CN-M",
    "CNS-C"
  ],
  "dailyBreakdown" : [
    {
      "day" : 1,
      "total" : 3.3024400000000001E+003,
      "discounts" : 2.7880999999999995E+002,
      "cash" : 1.6408699999999997E+003,
      "nonCash" : 1.6615700000000008E+003,
      "payments" : {
        "QR Code" : 1.6615700000000008E+003,
        "Cash" : 1.6408699999999997E+003,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 2,
      "total" : 3.3415299999999979E+003,
      "discounts" : 4.2488999999999993E+002,
      "cash" : 1.7078999999999996E+003,
      "nonCash" : 1.6336299999999999E+003,
      "payments" : {
        "QR Code" : 1.6336299999999999E+003,
        "Cash" : 1.7078999999999996E+003,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 3,
      "total" : 5.7949899999999998E+003,
      "discounts" : 5.7592999999999995E+002,
      "cash" : 4.0997399999999989E+003,
      "nonCash" : 1.6952500000000002E+003,
      "payments" : {
        "QR Code" : 4.2285000000000008E+002,
        "Cash" : 4.0997399999999989E+003,
        "QR" : 1.1277000000000000E+003,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 1.4469999999999999E+002
      }
    },
    {
      "day" : 4,
      "total" : 3.3030999999999999E+003,
      "discounts" : 3.2791999999999985E+002,
      "cash" : 1.2570999999999999E+003,
      "nonCash" : 2.0460000000000005E+003,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 1.2570999999999999E+003,
        "QR" : 1.9143999999999999E+003,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 1.3159999999999999E+002
      }
    },
    {
      "day" : 5,
      "total" : 7.2080000000000007E+002,
      "discounts" : 1.1355999999999999E+002,
      "cash" : 2.1569999999999999E+002,
      "nonCash" : 5.0510000000000002E+002,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 2.1569999999999999E+002,
        "QR" : 4.7600000000000006E+002,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 2.9100000000000001E+001
      }
    },
    {
      "day" : 6,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 7,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 8,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 9,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 10,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 11,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 12,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 13,
      "total" : 8.0000000000000004E-001,
      "discounts" : 2.3999999999999999E-001,
      "cash" : 8.0000000000000004E-001,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 8.0000000000000004E-001,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 14,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 15,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 16,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 17,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 18,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 19,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 20,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 21,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 22,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 23,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 24,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 25,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 26,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 27,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 28,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 29,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 30,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    },
    {
      "day" : 31,
      "total" : 0.0000000000000000E+000,
      "discounts" : 0.0000000000000000E+000,
      "cash" : 0.0000000000000000E+000,
      "nonCash" : 0.0000000000000000E+000,
      "payments" : {
        "QR Code" : 0.0000000000000000E+000,
        "Cash" : 0.0000000000000000E+000,
        "QR" : 0.0000000000000000E+000,
        "CN-M" : 0.0000000000000000E+000,
        "CNS-C" : 0.0000000000000000E+000
      }
    }
  ]
};
    const hourlyData = {
  "hours" : [
    {
      "hour" : 0,
      "total" : 9.3425999999999988E+002,
      "count" : 50,
      "average" : 1.8685199999999998E+001
    },
    {
      "hour" : 1,
      "total" : 8.1643999999999994E+002,
      "count" : 32,
      "average" : 2.5513749999999998E+001
    },
    {
      "hour" : 2,
      "total" : 3.0363000000000000E+002,
      "count" : 17,
      "average" : 1.7860588235294117E+001
    },
    {
      "hour" : 3,
      "total" : 9.2890000000000001E+001,
      "count" : 6,
      "average" : 1.5481666666666667E+001
    },
    {
      "hour" : 4,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 5,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 6,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 7,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 8,
      "total" : 0.0000000000000000E+000,
      "count" : 0,
      "average" : 0.0000000000000000E+000
    },
    {
      "hour" : 9,
      "total" : 1.2209799999999998E+003,
      "count" : 10,
      "average" : 1.2209799999999998E+002
    },
    {
      "hour" : 10,
      "total" : 1.8800000000000001E+001,
      "count" : 1,
      "average" : 1.8800000000000001E+001
    },
    {
      "hour" : 11,
      "total" : 1.4180899999999999E+003,
      "count" : 8,
      "average" : 1.7726124999999999E+002
    },
    {
      "hour" : 12,
      "total" : 2.9550999999999999E+002,
      "count" : 16,
      "average" : 1.8469374999999999E+001
    },
    {
      "hour" : 13,
      "total" : 6.0913000000000000E+002,
      "count" : 21,
      "average" : 2.9006190476190476E+001
    },
    {
      "hour" : 14,
      "total" : 5.5138000000000011E+002,
      "count" : 18,
      "average" : 3.0632222222222229E+001
    },
    {
      "hour" : 15,
      "total" : 4.9145000000000010E+002,
      "count" : 24,
      "average" : 2.0477083333333336E+001
    },
    {
      "hour" : 16,
      "total" : 7.3602000000000010E+002,
      "count" : 35,
      "average" : 2.1029142857142858E+001
    },
    {
      "hour" : 17,
      "total" : 7.0807999999999993E+002,
      "count" : 34,
      "average" : 2.0825882352941175E+001
    },
    {
      "hour" : 18,
      "total" : 7.3541999999999996E+002,
      "count" : 29,
      "average" : 2.5359310344827584E+001
    },
    {
      "hour" : 19,
      "total" : 2.1573999999999996E+003,
      "count" : 41,
      "average" : 5.2619512195121942E+001
    },
    {
      "hour" : 20,
      "total" : 1.3326799999999998E+003,
      "count" : 33,
      "average" : 4.0384242424242416E+001
    },
    {
      "hour" : 21,
      "total" : 8.3321000000000004E+002,
      "count" : 38,
      "average" : 2.1926578947368423E+001
    },
    {
      "hour" : 22,
      "total" : 1.6545200000000007E+003,
      "count" : 55,
      "average" : 3.0082181818181830E+001
    },
    {
      "hour" : 23,
      "total" : 1.5537700000000000E+003,
      "count" : 55,
      "average" : 2.8250363636363637E+001
    }
  ]
};

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2
      }).format(amount);
    }
    
    // Initialize charts when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      try {
        // Daily Sales Chart
        const dailyCtx = document.getElementById('dailySalesChart');
        if (dailyCtx) {
          // Prepare data for multiple series
          const datasets = [];

          // Check if we have breakdown data
          if (dailyData.cash && dailyData.nonCash && dailyData.discounts) {
            // Show 4 series: Total, Cash, Non-Cash, Discounts
            datasets.push({
              label: 'Total Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Cash',
              data: dailyData.cash || [],
              backgroundColor: 'rgba(75, 192, 192, 0.8)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Non-Cash',
              data: dailyData.nonCash || [],
              backgroundColor: 'rgba(255, 159, 64, 0.8)',
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 2
            });
            datasets.push({
              label: 'Discounts',
              data: dailyData.discounts || [],
              backgroundColor: 'rgba(255, 99, 132, 0.8)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 2
            });
          } else {
            // Fallback to single series
            datasets.push({
              label: 'Daily Sales',
              data: dailyData.amounts || [],
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 2
            });
          }

          new Chart(dailyCtx, {
            type: 'bar',
            data: {
              labels: dailyData.dates || [],
              datasets: datasets
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return formatCurrency(context.raw);
                    }
                  }
                },
                legend: {
                  display: datasets.length > 1
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Daily Sales Table
        const dailyTableBody = document.getElementById('dailyTableBody');
        const dailyTableHeader = document.getElementById('dailyTableHeader');
        if (dailyTableBody && dailyTableHeader && dailyData.dates && dailyData.amounts) {
          const paymentMethods = dailyData.paymentMethods || [];
          const dailyBreakdown = dailyData.dailyBreakdown || [];

          // Clear existing content
          dailyTableBody.innerHTML = '';
          dailyTableHeader.innerHTML = '';

          // Create header row
          const headerRow = document.createElement('tr');
          headerRow.innerHTML = `
            <th>Day</th>
            <th>Total Sales</th>
            <th>Discounts</th>
            <th>% of Month</th>
            ${paymentMethods.map(method => `<th>${method}</th>`).join('')}
          `;
          dailyTableHeader.appendChild(headerRow);

          const monthTotal = dailyData.amounts.reduce((sum, amount) => sum + amount, 0);

          // Add rows for each day
          dailyData.dates.forEach((date, index) => {
            const amount = dailyData.amounts[index] || 0;
            const discount = (dailyData.discounts && dailyData.discounts[index]) || 0;
            const percentage = monthTotal > 0 ? (amount / monthTotal * 100).toFixed(1) : '0.0';
            const dayNumber = index + 1; // Convert to 1-based day

            // Find payment breakdown for this day
            const dayBreakdown = dailyBreakdown.find(db => db.day === dayNumber);
            const payments = dayBreakdown ? dayBreakdown.payments : {};

            const displayAmount = amount > 0 ? formatCurrency(amount) : '';
            const displayDiscount = discount > 0 ? formatCurrency(discount) : '';
            const displayPercentage = amount > 0 ? percentage + '%' : '';

            // Generate payment method cells
            let paymentCells = '';
            paymentMethods.forEach(method => {
              const methodAmount = payments[method] || 0;
              const displayMethodAmount = methodAmount > 0 ? formatCurrency(methodAmount) : '';
              paymentCells += `<td class="payment-amount">${displayMethodAmount}</td>`;
            });

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${date}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-amount">${displayDiscount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
              ${paymentCells}
            `;
            dailyTableBody.appendChild(row);
          });

          // Add total row
          if (monthTotal > 0) {
            // Calculate totals for each column
            const totalDiscounts = (dailyData.discounts || []).reduce((sum, amount) => sum + amount, 0);

            // Calculate payment method totals
            let paymentTotalCells = '';
            paymentMethods.forEach(method => {
              let methodTotal = 0;
              dailyBreakdown.forEach(db => {
                methodTotal += db.payments[method] || 0;
              });
              paymentTotalCells += `<td class="payment-amount"><strong>${formatCurrency(methodTotal)}</strong></td>`;
            });

            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(monthTotal)}</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalDiscounts)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
              ${paymentTotalCells}
            `;
            dailyTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing daily sales chart:', error);
      }

      // Payment Methods Chart
      try {
        const paymentCtx = document.getElementById('paymentMethodsChart');
        if (paymentCtx) {
          new Chart(paymentCtx, {
            type: 'pie',
            data: {
              labels: Object.keys(paymentData.methods || {}),
              datasets: [{
                data: Object.values(paymentData.methods || {}),
                backgroundColor: [
                  '#3498db', '#2ecc71', '#e74c3c', '#f1c40f',
                  '#9b59b6', '#1abc9c', '#e67e22', '#74c0fc'
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Payment Methods Distribution',
                  font: { size: 16 }
                },
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = Math.round((value / total) * 100);
                      return `${label}: ${formatCurrency(value)} (${percentage}%)`;
                    }
                  }
                }
              }
            }
          });
        }

        // Populate Payment Methods Table
        const paymentTableBody = document.getElementById('paymentTableBody');
        if (paymentTableBody && paymentData.methods) {
          const methods = paymentData.methods;
          const methodNames = Object.keys(methods);
          const methodAmounts = Object.values(methods);

          // Calculate total for percentages
          const totalAmount = methodAmounts.reduce((sum, amount) => sum + amount, 0);

          // Sort methods by amount (descending)
          const sortedMethods = methodNames
            .map(name => ({ name, amount: methods[name] }))
            .sort((a, b) => b.amount - a.amount);

          // Clear existing content
          paymentTableBody.innerHTML = '';

          // Add rows for each payment method
          sortedMethods.forEach(method => {
            const percentage = totalAmount > 0 ? (method.amount / totalAmount * 100).toFixed(1) : '0.0';
            const displayAmount = method.amount > 0 ? formatCurrency(method.amount) : '';
            const displayPercentage = method.amount > 0 ? percentage + '%' : '';

            const row = document.createElement('tr');
            row.innerHTML = `
              <td class="payment-method-name">${method.name}</td>
              <td class="payment-amount">${displayAmount}</td>
              <td class="payment-percentage">${displayPercentage}</td>
            `;
            paymentTableBody.appendChild(row);
          });

          // Add total row
          if (totalAmount > 0) {
            const totalRow = document.createElement('tr');
            totalRow.className = 'table-total';
            totalRow.innerHTML = `
              <td><strong>Total</strong></td>
              <td class="payment-amount"><strong>${formatCurrency(totalAmount)}</strong></td>
              <td class="payment-percentage"><strong>100.0%</strong></td>
            `;
            paymentTableBody.appendChild(totalRow);
          }
        }
      } catch (error) {
        console.error('Error initializing payment methods chart:', error);
      }

      // Update summary content
      try {
        const summaryContent = document.getElementById('summary-content');
        if (summaryContent && monthlyData) {
          summaryContent.innerHTML =
            '<div class="summary-item">' +
            'Total Sales: <span class="summary-value">' + formatCurrency(monthlyData.totalSales || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Average Daily Sales: <span class="summary-value">' + formatCurrency(monthlyData.averageDaily || 0) + '</span>' +
            '</div>' +
            '<div class="summary-item">' +
            'Busiest Day: <span class="summary-value">' +
            (monthlyData.busiestDay ? monthlyData.busiestDay.day + ' (' + formatCurrency(monthlyData.busiestDay.amount) + ')' : 'N/A') +
            '</span></div>';
        }
      } catch (error) {
        console.error('Error updating summary content:', error);
      }

      // Hourly Distribution Chart
      try {
        const hourlyCtx = document.getElementById('hourlyDistributionChart');
        if (hourlyCtx) {
          new Chart(hourlyCtx.getContext('2d'), {
            type: 'line',
            data: {
              labels: Array.from({length: 24}, (_, i) => `${i}:00`),
              datasets: [{
                label: 'Average Sales',
                data: hourlyData.hours ? hourlyData.hours.map(hour => hour.average) : [],
                borderColor: 'rgba(231, 76, 60, 0.8)',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                tooltip: {
                  callbacks: {
                    label: function(context) {
                      return `Avg: ${formatCurrency(context.raw)}`;
                    }
                  }
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function(value) {
                      return formatCurrency(value);
                    }
                  }
                }
              }
            }
          });
        }
      } catch (error) {
        console.error('Error initializing hourly distribution chart:', error);
      }
    }); // Close DOMContentLoaded
  </script>
</body>
</html>
