<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SnP Reports</title>

    <!-- FirebaseUI CSS -->
    <link type="text/css" rel="stylesheet" href="https://www.gstatic.com/firebasejs/ui/6.1.0/firebase-ui-auth.css" />

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/ui/6.1.0/firebase-ui-auth.js"></script>
    
    <!-- Firebase Config -->
    <script>
        // Initialize Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyDbZq0cj8MoegdQcOKyrjXnWGSbRQl8Ito",
            authDomain: "snpweb-667b7.firebaseapp.com",
            projectId: "snpweb-667b7",
            storageBucket: "snpweb-667b7.firebasestorage.app",
            messagingSenderId: "1010342767989",
            appId: "1:1010342767989:web:95c3c40944cbb728a0773c"
        };
        
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        
        // Make auth available globally
        const auth = firebase.auth();
        const PhoneAuthProvider = firebase.auth.PhoneAuthProvider;
        const onAuthStateChanged = firebase.auth().onAuthStateChanged;
    </script>

    <style>
        /* Styling for the container */
        #firebaseui-auth-container {
            margin: 20px auto;
            width: 100%;
            max-width: 400px;
            border: 1px solid #ddd;
            padding: 20px;
            box-shadow: 2px 2px 5px rgba(0,0,0,0.1);
            border-radius: 8px;
            box-sizing: border-box;
        } 
        /* Make the phone input more compact */
        .firebaseui-container {
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .firebaseui-idp-button {
            max-width: 100% !important;
        }
        
        .firebaseui-card-content {
            padding: 0 !important;
        }
        
        .firebaseui-textfield.mdl-textfield {
            width: 100% !important;
        }
        
        .firebaseui-phone-number-selector {
            width: 100% !important;
        }
        
        .firebaseui-phone-input {
            width: 100% !important;
        }
        
        #loader {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
        }
        
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
        }
        .mdl-shadow--2dp {
            box-shadow: none !important; /* Remove shadow completely */
            /* OR customize with your own shadow */
            /* box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important; */
        }  
        .firebaseui-resend-container {
            direction: ltr;
            margin: 20px 0;
            text-align: center;
            max-height: 500px !important;
        }    
    </style>
</head>
<body>
    <div style="text-align: right; padding: 10px;">
        <button id="sign-out-button" style="display: none; padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Sign Out
        </button>
    </div>
    <h1>Welcome to SnP Reports</h1>
    <div id="loader">Loading authentication...</div>
    <div id="firebaseui-auth-container"></div>
    <div id="recaptcha-container"></div>

    <script>
        // Firebase is already initialized in the global scope
        
        const loader = document.getElementById('loader');
        
        // Temporarily disabling reCAPTCHA for testing
        console.log('reCAPTCHA verification is disabled for testing');
        //window.recaptchaVerifier = {
        //    verify: () => Promise.resolve('test-verification-id')
        //};

        // Initialize FirebaseUI
        const uiConfig = {
            signInOptions: [
                {
                    provider: PhoneAuthProvider.PROVIDER_ID,
                    // reCAPTCHA parameters temporarily disabled for testing
                    recaptchaParameters: {
                        type: 'invisible',  // Makes reCAPTCHA invisible
                        // OR to disable it completely (not recommended for production)
                        size: 'invisible'
                    },
                    defaultCountry: 'MY',  // Malaysia country code
                    defaultNationalNumber: '',
                    loginHint: '+60',
                    whitelistedCountries: ['MY', 'SG', 'ID', 'TH', 'PH', 'VN']
                }
            ],
            callbacks: {
                signInSuccessWithAuthResult: function(authResult, redirectUrl) {
                    // User successfully signed in
                    console.log("Sign-in successful!", authResult.user);
                    window.location.href = 'dashboard.html';
                    return false; // Prevent redirect
                },
                uiShown: function() {
                    if (loader) loader.style.display = 'none';
                }
            },
            signInFlow: 'popup',
            //tosUrl: 'https://example.com/terms-of-service',
            //privacyPolicyUrl: 'https://example.com/privacy-policy',
            immediateFederatedRedirect: false
        };

        // Get UI elements
        const signOutButton = document.getElementById('sign-out-button');
        
        // Sign out function
        function signOut() {
            auth.signOut().then(() => {
                console.log('User signed out');
                // Reset UI
                signOutButton.style.display = 'none';
                // Redirect to login page
                window.location.href = 'index.html';
            }).catch((error) => {
                console.error('Sign out error:', error);
            });
        }
        
        // Add event listener to sign out button
        signOutButton.addEventListener('click', signOut);
        
        // Check auth state first
        auth.onAuthStateChanged((user) => {
            if (loader) loader.style.display = 'none';
            
            if (user) {
                // User is signed in
                console.log('User is signed in:', user.uid);
                // Show sign out button
                signOutButton.style.display = 'inline-block';
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
            } else {
                // User is signed out
                console.log('No user signed in, initializing FirebaseUI');
                // Hide sign out button
                signOutButton.style.display = 'none';
                // Initialize FirebaseUI only when needed
                const ui = firebaseui.auth.AuthUI.getInstance() || new firebaseui.auth.AuthUI(auth);
                
                // Start the FirebaseUI widget
                ui.start('#firebaseui-auth-container', uiConfig);
            }
        });
    </script>
</body>
</body>
</html>
