import { writeFileSync } from 'fs';
import { join, basename, dirname } from 'path';
import { fileURLToPath } from 'url';
import { glob } from 'glob';

const __dirname = dirname(fileURLToPath(import.meta.url));

async function generateReportsList() {
  const reportsDir = join(__dirname, 'reports');
  const outputFile = join(__dirname, 'reports', 'reports.json');
  
  // Find all HTML reports (search in the reports directory and one level down)
  const files = [
    ...(await glob(join(reportsDir, '*.html'))),  // Files directly in reports directory
    ...(await glob(join(reportsDir, '**/*.html'))) // Files in subdirectories
  ].filter((file, index, self) => 
    // Remove duplicates (in case the same file matches both patterns)
    self.indexOf(file) === index
  );
  
  const reports = {
    lastUpdated: new Date().toISOString(),
    yearly: [],
    monthly: {}
  };

  files.forEach(file => {
    const filename = basename(file);
    const matchYearly = filename.match(/^yearly_report_(\d{4})\.html$/);
    const matchMonthly = filename.match(/^monthly_report_(\d{4})_(\d{2})\.html$/);

    if (matchYearly) {
      const year = parseInt(matchYearly[1], 10);
      if (!reports.yearly.includes(year)) {
        reports.yearly.push(year);
      }
    } else if (matchMonthly) {
      const year = parseInt(matchMonthly[1], 10);
      const month = parseInt(matchMonthly[2], 10);
      
      if (!reports.monthly[year]) {
        reports.monthly[year] = [];
      }
      if (!reports.monthly[year].includes(month)) {
        reports.monthly[year].push(month);
      }
    }
  });

  // Sort years in descending order
  reports.yearly.sort((a, b) => b - a);
  
  // Sort months in ascending order for each year
  Object.values(reports.monthly).forEach(months => months.sort((a, b) => a - b));

  // Write the reports list to a JSON file
  writeFileSync(outputFile, JSON.stringify(reports, null, 2));
  console.log(`Generated ${outputFile} with ${reports.yearly.length} yearly and ${Object.keys(reports.monthly).length} monthly reports`);
}

// Run the function
generateReportsList().catch(console.error);
