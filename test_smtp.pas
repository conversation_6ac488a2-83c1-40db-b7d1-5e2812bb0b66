program test_smtp;

{$mode objfpc}{$H+}

uses
  Classes, SysUtils, IniFiles,
  smtpsend, ssl_openssl3, mimemess, mimepart;

procedure TestSMTPConnection;
var
  SMTP: TSMTPSend;
  Msg: TMimeMess;
  Part: TMimePart;
  BodyLines: TStringList;
  ConfigFile: TIniFile;
  SMTPHost, SMTPUser, SMTPPass, SMTPFrom, SSLStr: String;
  SMTPPort: Integer;
  UseSSL: Boolean;
begin
  WriteLn('SMTP Connection Test');
  WriteLn('===================');
  
  // Load config
  ConfigFile := TIniFile.Create('config.ini');
  try
    SMTPHost := ConfigFile.ReadString('SMTP', 'Host', 'smtp.gmail.com');
    SMTPPort := ConfigFile.ReadInteger('SMTP', 'Port', 587);
    // Read UseSSL more robustly
    SSLStr := LowerCase(Trim(ConfigFile.ReadString('SMTP', 'UseSSL', 'true')));
    UseSSL := (SSLStr = 'true') or (SSLStr = '1') or (SSLStr = 'yes');
    WriteLn('Debug: Raw UseSSL value: "', ConfigFile.ReadString('SMTP', 'UseSSL', 'default'), '"');
    WriteLn('Debug: Parsed UseSSL: ', BoolToStr(UseSSL, True));
    SMTPUser := ConfigFile.ReadString('SMTP', 'Username', '');
    SMTPPass := ConfigFile.ReadString('SMTP', 'Password', '');
    SMTPFrom := ConfigFile.ReadString('SMTP', 'FromEmail', SMTPUser);
  finally
    ConfigFile.Free;
  end;
  
  WriteLn('Configuration:');
  WriteLn('  Host: ', SMTPHost);
  WriteLn('  Port: ', SMTPPort);
  WriteLn('  SSL: ', BoolToStr(UseSSL, True));
  WriteLn('  User: ', SMTPUser);
  WriteLn('  From: ', SMTPFrom);
  WriteLn('');
  
  if (SMTPUser = '') or (SMTPPass = '') then
  begin
    WriteLn('ERROR: SMTP credentials not configured in config.ini');
    Exit;
  end;
  
  SMTP := TSMTPSend.Create;
  Msg := TMimeMess.Create;
  BodyLines := TStringList.Create;
  try
    // Configure SMTP
    SMTP.TargetHost := SMTPHost;
    SMTP.TargetPort := IntToStr(SMTPPort);
    SMTP.Username := SMTPUser;
    SMTP.Password := SMTPPass;
    SMTP.Timeout := 60000; // 60 seconds timeout
    
    if UseSSL then
    begin
      if SMTPPort = 465 then
      begin
        SMTP.FullSSL := True;  // SSL
        SMTP.AutoTLS := False;
        WriteLn('Using SSL (port 465)');
      end
      else
      begin
        SMTP.AutoTLS := True;  // TLS (STARTTLS)
        SMTP.FullSSL := False;
        WriteLn('Using TLS/STARTTLS (port 587)');
      end;
    end
    else
    begin
      SMTP.AutoTLS := False;
      SMTP.FullSSL := False;
      WriteLn('Using plain SMTP (no encryption)');
    end;
    
    WriteLn('');
    WriteLn('Attempting SMTP connection...');
    WriteLn('Host: ', SMTP.TargetHost);
    WriteLn('Port: ', SMTP.TargetPort);
    WriteLn('Username: ', SMTP.Username);
    WriteLn('AutoTLS: ', BoolToStr(SMTP.AutoTLS, True));
    WriteLn('FullSSL: ', BoolToStr(SMTP.FullSSL, True));
    WriteLn('');
    
    if SMTP.Login then
    begin
      WriteLn('SUCCESS: Connected to SMTP server!');
      WriteLn('');
      
      // Create a simple test message
      Msg.Header.From := SMTPFrom;
      Msg.Header.ToList.Add('<EMAIL>');
      Msg.Header.Subject := 'SMTP Test - ' + FormatDateTime('yyyy-mm-dd hh:nn:ss', Now);
      Msg.Header.Date := Now;
      
      // Add simple text body
      Part := Msg.AddPartMultipart('mixed', nil);
      BodyLines.Add('This is a test email from the SNP Report Generator.');
      BodyLines.Add('');
      BodyLines.Add('If you receive this email, the SMTP configuration is working correctly.');
      BodyLines.Add('');
      BodyLines.Add('Sent at: ' + FormatDateTime('yyyy-mm-dd hh:nn:ss', Now));
      Msg.AddPartText(BodyLines, Part);
      
      WriteLn('Sending test email...');
      if SMTP.MailFrom(SMTPFrom, Length(Msg.Lines.Text)) then
      begin
        if SMTP.MailTo('<EMAIL>') then
        begin
          if SMTP.MailData(Msg.Lines) then
          begin
            WriteLn('SUCCESS: Test email sent successfully!');
            WriteLn('Check <EMAIL> for the test message.');
          end
          else
          begin
            WriteLn('FAILED: Could not send email data');
            WriteLn('Error: ', SMTP.ResultString);
          end;
        end
        else
        begin
          WriteLn('FAILED: Could not set recipient');
          WriteLn('Error: ', SMTP.ResultString);
        end;
      end
      else
      begin
        WriteLn('FAILED: Could not set sender');
        WriteLn('Error: ', SMTP.ResultString);
      end;
      
      SMTP.Logout;
    end
    else
    begin
      WriteLn('FAILED: Could not connect to SMTP server');
      WriteLn('Error Code: ', SMTP.ResultCode);
      WriteLn('Error Message: ', SMTP.ResultString);
      WriteLn('Full Response:');
      WriteLn(SMTP.FullResult.Text);
      WriteLn('');
      WriteLn('Common issues:');
      WriteLn('1. Check Gmail App Password (not regular password)');
      WriteLn('2. Ensure 2FA is enabled on Gmail account');
      WriteLn('3. Check firewall/antivirus blocking port 587');
      WriteLn('4. Verify internet connection');
    end;
    
  finally
    BodyLines.Free;
    Msg.Free;
    SMTP.Free;
  end;
end;

begin
  try
    TestSMTPConnection;
  except
    on E: Exception do
      WriteLn('EXCEPTION: ', E.ClassName, ': ', E.Message);
  end;
  
  WriteLn('');
  WriteLn('Press Enter to exit...');
  ReadLn;
end.
