unit uReports;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, DateUtils, StrUtils, fpjson, jsonparser, uDB, uUtils, Contnrs;

type
  TSnpReport = class
  private
    FDatabase: TSnpDatabase;
    FTemplatePath: string;
    FReportsPath: string;
    function LoadTemplate(const TemplateName: string): string;
    function GenerateYearlyData(Year: Integer): string;
    function GenerateMonthlyData(Year, Month: Integer): string;
    function GeneratePaymentMethodsData(Year, Month: Integer): string;
    function GenerateDailySalesData(Year, Month: Integer): string;
    function GenerateHourlyData(Year, Month: Integer): string;
  public
    constructor Create(Database: TSnpDatabase);
    procedure GenerateYearlyReport(Year: Integer; const OutputDir: string);
    procedure GenerateMonthlyReport(Year, Month: Integer; const OutputDir: string);
    procedure UpdateReportsList(const OutputDir: string = '');
  end;

implementation

{ TSnpReport }

constructor TSnpReport.Create(Database: TSnpDatabase);
begin
  inherited Create;
  FDatabase := Database;
  FTemplatePath := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0)) + 'templates');
  FReportsPath := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0)) + 'reports');
  
  if not DirectoryExists(FTemplatePath) then
    CreateDir(FTemplatePath);
  if not DirectoryExists(FReportsPath) then
    CreateDir(FReportsPath);
end;

function TSnpReport.LoadTemplate(const TemplateName: string): string;
var
  TemplateFile: TStringList;
  TemplatePath: string;
begin
  TemplatePath := IncludeTrailingPathDelimiter(FTemplatePath) + TemplateName;
  if not FileExists(TemplatePath) then
    raise Exception.CreateFmt('Template not found: %s', [TemplatePath]);
    
  TemplateFile := TStringList.Create;
  try
    TemplateFile.LoadFromFile(TemplatePath);
    Result := TemplateFile.Text;
  finally
    TemplateFile.Free;
  end;
end;

function TSnpReport.GenerateYearlyData(Year: Integer): string;
var
  Sessions: TSessionArray;
  MonthlyTotals: array[1..12] of Double;
  MonthlyDiscounts: array[1..12] of Double;
  PaymentTotals: TStringList;
  MonthlyPaymentTotals: array[1..12] of TStringList;
  i, j, k: Integer;
  Month, Day: Word;
  Total, MonthlyAvg, YearlyTotal, YearlyDiscounts: Double;
  BestMonth: Integer;
  BestAmount: Double;
  ResultJSON: TStringList;
  DummyYear, DummyMonth, DummyDay: Word;
  CurrentValue: Integer;
  PaymentMethod: string;
  PaymentIndex: Integer;
begin
  // Initialize
  Sessions := FDatabase.GetSessionsByYear(Year);
  FillChar(MonthlyTotals, SizeOf(MonthlyTotals), 0);
  FillChar(MonthlyDiscounts, SizeOf(MonthlyDiscounts), 0);
  PaymentTotals := TStringList.Create;

  // Initialize monthly payment totals arrays
  for i := 1 to 12 do
    MonthlyPaymentTotals[i] := TStringList.Create;

  try
    // First pass: collect all unique payment methods
    for i := 0 to High(Sessions) do
    begin
      PaymentMethod := Trim(Sessions[i].PaymentMethod);
      if PaymentMethod = '' then
        PaymentMethod := 'Unknown';

      if PaymentTotals.IndexOf(PaymentMethod) = -1 then
        PaymentTotals.Add(PaymentMethod);
    end;

    // Initialize all monthly payment lists with all payment methods
    for i := 1 to 12 do
    begin
      for j := 0 to PaymentTotals.Count - 1 do
        MonthlyPaymentTotals[i].AddObject(PaymentTotals[j], TObject(0));
    end;

    // Second pass: calculate monthly totals and payment method breakdowns
    for i := 0 to High(Sessions) do
    begin
      DecodeDate(Sessions[i].StartTime, DummyYear, DummyMonth, DummyDay);
      Month := DummyMonth;
      MonthlyTotals[Month] := MonthlyTotals[Month] + Sessions[i].FinalAmount;
      MonthlyDiscounts[Month] := MonthlyDiscounts[Month] + Sessions[i].MemberDiscount + Sessions[i].OperatorDiscount;

      // Track payment methods by month
      PaymentMethod := Trim(Sessions[i].PaymentMethod);
      if PaymentMethod = '' then
        PaymentMethod := 'Unknown';

      PaymentIndex := MonthlyPaymentTotals[Month].IndexOf(PaymentMethod);
      if PaymentIndex >= 0 then
      begin
        CurrentValue := PtrInt(MonthlyPaymentTotals[Month].Objects[PaymentIndex]);
        MonthlyPaymentTotals[Month].Objects[PaymentIndex] :=
          TObject(PtrInt(CurrentValue + Trunc(Sessions[i].FinalAmount * 100)));
      end;
    end;
    
    // Calculate yearly total and find best month
    YearlyTotal := 0;
    YearlyDiscounts := 0;
    BestAmount := 0;
    BestMonth := 1;
    for i := 1 to 12 do
    begin
      YearlyTotal := YearlyTotal + MonthlyTotals[i];
      YearlyDiscounts := YearlyDiscounts + MonthlyDiscounts[i];
      if MonthlyTotals[i] > BestAmount then
      begin
        BestAmount := MonthlyTotals[i];
        BestMonth := i;
      end;
    end;
    MonthlyAvg := YearlyTotal / 12;
    
    // Build JSON result
    ResultJSON := TStringList.Create;
    try
      ResultJSON.Add('{');
      ResultJSON.Add(Format('  "year": %d,', [Year]));
      ResultJSON.Add('  "monthlyTotals": [');
      for i := 1 to 12 do
      begin
        ResultJSON.Add(Format('    %s%.2f',
          [IfThen(i > 1, ','), MonthlyTotals[i]]));
      end;
      ResultJSON.Add('  ],');

      ResultJSON.Add('  "monthlyDiscounts": [');
      for i := 1 to 12 do
      begin
        ResultJSON.Add(Format('    %s%.2f',
          [IfThen(i > 1, ','), MonthlyDiscounts[i]]));
      end;
      ResultJSON.Add('  ],');

      // Add monthly payment breakdown
      ResultJSON.Add('  "monthlyPaymentBreakdown": [');
      for i := 1 to 12 do
      begin
        ResultJSON.Add(Format('    %s{', [IfThen(i > 1, ',')]));
        ResultJSON.Add(Format('      "month": %d,', [i]));
        ResultJSON.Add('      "payments": {');
        for j := 0 to MonthlyPaymentTotals[i].Count - 1 do
        begin
          CurrentValue := PtrInt(MonthlyPaymentTotals[i].Objects[j]);
          ResultJSON.Add(Format('        %s"%s": %.2f',
            [IfThen(j > 0, ','), MonthlyPaymentTotals[i][j], CurrentValue / 100.0]));
        end;
        ResultJSON.Add('      }');
        ResultJSON.Add('    }');
      end;
      ResultJSON.Add('  ],');

      // Add payment methods list
      ResultJSON.Add('  "paymentMethods": [');
      for i := 0 to PaymentTotals.Count - 1 do
      begin
        ResultJSON.Add(Format('    %s"%s"', [IfThen(i > 0, ','), PaymentTotals[i]]));
      end;
      ResultJSON.Add('  ],');

      // Add cash vs non-cash breakdown
      ResultJSON.Add('  "monthlyCashBreakdown": [');
      for i := 1 to 12 do
      begin
        // Calculate cash and non-cash totals for this month
        Total := 0; // Cash total
        MonthlyAvg := 0; // Non-cash total
        for j := 0 to MonthlyPaymentTotals[i].Count - 1 do
        begin
          CurrentValue := PtrInt(MonthlyPaymentTotals[i].Objects[j]);
          // Consider "Cash" as cash, everything else as non-cash
          if (UpperCase(MonthlyPaymentTotals[i][j]) = 'CASH') or
             (UpperCase(MonthlyPaymentTotals[i][j]) = 'TUNAI') then
            Total := Total + (CurrentValue / 100.0)
          else
            MonthlyAvg := MonthlyAvg + (CurrentValue / 100.0);
        end;

        ResultJSON.Add(Format('    %s{', [IfThen(i > 1, ',')]));
        ResultJSON.Add(Format('      "month": %d,', [i]));
        ResultJSON.Add(Format('      "total": %.2f,', [MonthlyTotals[i]]));
        ResultJSON.Add(Format('      "cash": %.2f,', [Total]));
        ResultJSON.Add(Format('      "nonCash": %.2f', [MonthlyAvg]));
        ResultJSON.Add('    }');
      end;
      ResultJSON.Add('  ],');

      ResultJSON.Add(Format('  "yearlyTotal": %.2f,', [YearlyTotal]));
      ResultJSON.Add(Format('  "yearlyDiscounts": %.2f,', [YearlyDiscounts]));
      ResultJSON.Add(Format('  "averageMonthly": %.2f,', [MonthlyAvg]));
      ResultJSON.Add(Format('  "bestMonth": {', []));
      ResultJSON.Add(Format('    "month": %d,', [BestMonth]));
      ResultJSON.Add(Format('    "amount": %.2f', [BestAmount]));
      ResultJSON.Add('  }');
      ResultJSON.Add('}');

      Result := ResultJSON.Text;
    finally
      ResultJSON.Free;
    end;
  finally
    PaymentTotals.Free;
    // Clean up monthly payment totals arrays
    for i := 1 to 12 do
      MonthlyPaymentTotals[i].Free;
  end;
end;

function TSnpReport.GeneratePaymentMethodsData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  PaymentTotals: TStringList;
  i, j: Integer;
  PaymentMethod: string;
  PaymentAmounts: array of Double;
  
  // JSON objects
  jData: TJSONObject;
  jMethods: TJSONObject;
begin
  if Month > 0 then
    Sessions := FDatabase.GetSessionsByMonth(Year, Month)
  else
    Sessions := FDatabase.GetSessionsByYear(Year);
    
  PaymentTotals := TStringList.Create;
  try
    // First pass: collect unique payment methods
    for i := 0 to High(Sessions) do
    begin
      PaymentMethod := Trim(Sessions[i].PaymentMethod);
      if PaymentMethod = '' then
        PaymentMethod := 'Unknown';
        
      if PaymentTotals.IndexOf(PaymentMethod) = -1 then
        PaymentTotals.Add(PaymentMethod);
    end;
    
    // Initialize amounts array
    SetLength(PaymentAmounts, PaymentTotals.Count);
    for i := 0 to High(PaymentAmounts) do
      PaymentAmounts[i] := 0;
    
    // Second pass: sum amounts by payment method
    for i := 0 to High(Sessions) do
    begin
      PaymentMethod := Trim(Sessions[i].PaymentMethod);
      if PaymentMethod = '' then
        PaymentMethod := 'Unknown';
        
      j := PaymentTotals.IndexOf(PaymentMethod);
      if j >= 0 then
        PaymentAmounts[j] := PaymentAmounts[j] + Sessions[i].FinalAmount;
    end;
    
    // Build JSON structure
    jData := TJSONObject.Create;
    try
      // Create methods object with payment methods and their totals
      jMethods := TJSONObject.Create;
      for i := 0 to PaymentTotals.Count - 1 do
      begin
        jMethods.Add(PaymentTotals[i], StrToFloat(FormatFloat('0.00', PaymentAmounts[i])));
      end;
      
      // Add methods object to main data object
      jData.Add('methods', jMethods);
      
      // Format the JSON with nice indentation
      Result := jData.FormatJSON([], 2);
    finally
      jData.Free;
    end;
  finally
    PaymentTotals.Free;
  end;
end;

procedure TSnpReport.GenerateYearlyReport(Year: Integer; const OutputDir: string);
var
  Template, OutputFile: string;
  Output: TStringList;
  YearlyData, PaymentData: string;
begin
  // Generate data
  YearlyData := GenerateYearlyData(Year);
  PaymentData := GeneratePaymentMethodsData(Year, 0);
  
  // Load and process template
  Template := LoadTemplate('yearly.html');
  
  // Replace placeholders with properly escaped JSON data
  Template := StringReplace(Template, '{{year}}', IntToStr(Year), [rfReplaceAll]);
  Template := StringReplace(Template, '{{generated_date}}', 
    FormatDateTime('yyyy-mm-dd hh:nn:ss', Now), [rfReplaceAll]);
  Template := StringReplace(Template, '{{monthly_data}}', EscapeJSON(YearlyData), [rfReplaceAll]);
  Template := StringReplace(Template, '{{payment_data}}', EscapeJSON(PaymentData), [rfReplaceAll]);
  
  // Save output
  ForceDirectories(OutputDir);
  OutputFile := IncludeTrailingPathDelimiter(OutputDir) + 
    Format('yearly_report_%d.html', [Year]);
    
  Output := TStringList.Create;
  try
    Output.Text := Template;
    Output.SaveToFile(OutputFile);
  finally
    Output.Free;
  end;
  UpdateReportsList(OutputDir);
end;

procedure TSnpReport.GenerateMonthlyReport(Year, Month: Integer; const OutputDir: string);
var
  Template, OutputFile: string;
  Output: TStringList;
  MonthlyData, PaymentData, DailyData, HourlyData: string;
begin
  // Generate data
  MonthlyData := GenerateMonthlyData(Year, Month);
  PaymentData := GeneratePaymentMethodsData(Year, Month);
  DailyData := GenerateDailySalesData(Year, Month);
  HourlyData := GenerateHourlyData(Year, Month);
  
  // Load template
  Template := LoadTemplate('monthly.html');
  
  // Replace placeholders with data
  Template := StringReplace(Template, '{{year}}', IntToStr(Year), [rfReplaceAll]);
  Template := StringReplace(Template, '{{month}}', IntToStr(Month), [rfReplaceAll]);
  Template := StringReplace(Template, '{{generated_date}}', 
    FormatDateTime('yyyy-mm-dd hh:nn:ss', Now), [rfReplaceAll]);
    
  // Replace JSON data placeholders with properly formatted JavaScript variables
  Template := StringReplace(Template, 
    'const monthlyData = JSON.parse(''{{monthly_data}}'');', 
    'const monthlyData = ' + MonthlyData + ';', 
    [rfReplaceAll, rfIgnoreCase]);
    
  Template := StringReplace(Template, 
    'const paymentData = JSON.parse(''{{payment_data}}'');', 
    'const paymentData = ' + PaymentData + ';', 
    [rfReplaceAll, rfIgnoreCase]);
    
  Template := StringReplace(Template, 
    'const dailyData = JSON.parse(''{{daily_data}}'');', 
    'const dailyData = ' + DailyData + ';', 
    [rfReplaceAll, rfIgnoreCase]);
    
  // Also replace the direct JSON.parse versions that might exist
  Template := StringReplace(Template, 
    'JSON.parse(''{{monthly_data}}'')', 
    MonthlyData, 
    [rfReplaceAll, rfIgnoreCase]);
    
  Template := StringReplace(Template, 
    'JSON.parse(''{{payment_data}}'')', 
    PaymentData, 
    [rfReplaceAll, rfIgnoreCase]);
    
  Template := StringReplace(Template, 
    'JSON.parse(''{{daily_data}}'')', 
    DailyData, 
    [rfReplaceAll, rfIgnoreCase]);
    
  Template := StringReplace(Template, 
    'const hourlyData = JSON.parse(''{{hourly_data}}'');', 
    'const hourlyData = ' + HourlyData + ';', 
    [rfReplaceAll, rfIgnoreCase]);
  
  // Save output
  ForceDirectories(OutputDir);
  OutputFile := IncludeTrailingPathDelimiter(OutputDir) + 
    Format('monthly_report_%d_%0.2d.html', [Year, Month]);
    
  Output := TStringList.Create;
  try
    Output.Text := Template;
    Output.SaveToFile(OutputFile);
  finally
    Output.Free;
  end;
  UpdateReportsList(OutputDir);
end;

function TSnpReport.GenerateMonthlyData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  DaysInMonth, i: Integer;
  DailyTotals: array of Double;
  TotalSales, DailySum: Double;
  BusiestDay: Integer;
  BusiestAmount: Double;
  Day, M, Y: Word;
  
  // JSON objects
  jData, jBusiestDay: TJSONObject;
begin
  Sessions := FDatabase.GetSessionsByMonth(Year, Month);
  
  // Calculate days in month
  DaysInMonth := DaysInAMonth(Year, Month);
  SetLength(DailyTotals, DaysInMonth + 1); // 1-based array
  
  // Initialize totals
  TotalSales := 0;
  BusiestAmount := 0;
  BusiestDay := 1;
  
  // Calculate daily totals
  for i := 0 to High(Sessions) do
  begin
    DecodeDate(Sessions[i].StartTime, Y, M, Day);
    if (Y = Year) and (M = Month) and (Day <= DaysInMonth) then
    begin
      DailyTotals[Day] := DailyTotals[Day] + Sessions[i].FinalAmount;
      TotalSales := TotalSales + Sessions[i].FinalAmount;
      
      if DailyTotals[Day] > BusiestAmount then
      begin
        BusiestAmount := DailyTotals[Day];
        BusiestDay := Day;
      end;
    end;
  end;
  
  // Calculate average daily sales (excluding days with no sales)
  DailySum := 0;
  for i := 1 to DaysInMonth do
    DailySum := DailySum + DailyTotals[i];
    
  // Build JSON structure
  jData := TJSONObject.Create;
  try
    jBusiestDay := TJSONObject.Create;
    try
      // Add busiest day details
      jBusiestDay.Add('day', BusiestDay);
      jBusiestDay.Add('amount', StrToFloat(FormatFloat('0.00', BusiestAmount)));
      
      // Add all data to main object
      jData.Add('year', Year);
      jData.Add('month', Month);
      jData.Add('totalSales', StrToFloat(FormatFloat('0.00', TotalSales)));
      jData.Add('averageDaily', StrToFloat(FormatFloat('0.00', DailySum / DaysInMonth)));
      jData.Add('busiestDay', jBusiestDay);
      
      // Format the JSON with nice indentation
      Result := jData.FormatJSON([], 2);
    finally
      // jBusiestDay is owned by jData and will be freed with it
    end;
  finally
    jData.Free;
  end;
end;

function TSnpReport.GenerateDailySalesData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  DaysInMonth, i, j: Integer;
  Day: Word;
  DailyTotals: array of Double;
  DailyDiscounts: array of Double;
  DailyCash: array of Double;
  DailyNonCash: array of Double;
  PaymentMethods: TStringList;
  DailyPaymentTotals: array of array of Double;
  PaymentMethod: string;
  PaymentIndex: Integer;

  // JSON objects
  jData: TJSONObject;
  jDays, jAmounts, jDiscounts, jCash, jNonCash: TJSONArray;
  jDailyBreakdown: TJSONArray;
  jDayData, jPayments: TJSONObject;
  jPaymentMethods: TJSONArray;
begin
  Sessions := FDatabase.GetSessionsByMonth(Year, Month);
  PaymentMethods := TStringList.Create;

  try
    // Initialize daily arrays
    DaysInMonth := DaysInAMonth(Year, Month);
    SetLength(DailyTotals, DaysInMonth + 1); // 1-based array
    SetLength(DailyDiscounts, DaysInMonth + 1);
    SetLength(DailyCash, DaysInMonth + 1);
    SetLength(DailyNonCash, DaysInMonth + 1);

    // First pass: collect all unique payment methods
    for i := 0 to High(Sessions) do
    begin
      PaymentMethod := Trim(Sessions[i].PaymentMethod);
      if PaymentMethod = '' then
        PaymentMethod := 'Unknown';

      if PaymentMethods.IndexOf(PaymentMethod) = -1 then
        PaymentMethods.Add(PaymentMethod);
    end;

    // Initialize daily payment totals array
    SetLength(DailyPaymentTotals, DaysInMonth + 1);
    for i := 1 to DaysInMonth do
    begin
      SetLength(DailyPaymentTotals[i], PaymentMethods.Count);
      for j := 0 to PaymentMethods.Count - 1 do
        DailyPaymentTotals[i][j] := 0;
    end;

    // Second pass: calculate daily totals, discounts, and payment method breakdowns
    for i := 0 to High(Sessions) do
    begin
      Day := DayOfTheMonth(Sessions[i].StartTime);
      if (Day >= 1) and (Day <= DaysInMonth) then
      begin
        DailyTotals[Day] := DailyTotals[Day] + Sessions[i].FinalAmount;
        DailyDiscounts[Day] := DailyDiscounts[Day] + Sessions[i].MemberDiscount + Sessions[i].OperatorDiscount;

        // Categorize payment methods for cash/non-cash
        PaymentMethod := UpperCase(Trim(Sessions[i].PaymentMethod));
        if (PaymentMethod = 'CASH') or (PaymentMethod = 'TUNAI') then
          DailyCash[Day] := DailyCash[Day] + Sessions[i].FinalAmount
        else
          DailyNonCash[Day] := DailyNonCash[Day] + Sessions[i].FinalAmount;

        // Track detailed payment methods
        PaymentMethod := Trim(Sessions[i].PaymentMethod);
        if PaymentMethod = '' then
          PaymentMethod := 'Unknown';

        PaymentIndex := PaymentMethods.IndexOf(PaymentMethod);
        if PaymentIndex >= 0 then
          DailyPaymentTotals[Day][PaymentIndex] := DailyPaymentTotals[Day][PaymentIndex] + Sessions[i].FinalAmount;
      end;
    end;
  
    // Create JSON structure
    jData := TJSONObject.Create;
    try
      jDays := TJSONArray.Create;
      jAmounts := TJSONArray.Create;
      jDiscounts := TJSONArray.Create;
      jCash := TJSONArray.Create;
      jNonCash := TJSONArray.Create;
      jDailyBreakdown := TJSONArray.Create;
      jPaymentMethods := TJSONArray.Create;

      try
        // Add payment methods list
        for i := 0 to PaymentMethods.Count - 1 do
          jPaymentMethods.Add(PaymentMethods[i]);

        // Add dates and amounts to arrays
        for i := 1 to DaysInMonth do
        begin
          jDays.Add(Format('%.2d', [i]));
          jAmounts.Add(DailyTotals[i]);
          jDiscounts.Add(DailyDiscounts[i]);
          jCash.Add(DailyCash[i]);
          jNonCash.Add(DailyNonCash[i]);

          // Create detailed daily breakdown with all payment methods
          jDayData := TJSONObject.Create;
          jDayData.Add('day', i);
          jDayData.Add('total', DailyTotals[i]);
          jDayData.Add('discounts', DailyDiscounts[i]);
          jDayData.Add('cash', DailyCash[i]);
          jDayData.Add('nonCash', DailyNonCash[i]);

          // Add payment methods breakdown
          jPayments := TJSONObject.Create;
          for j := 0 to PaymentMethods.Count - 1 do
            jPayments.Add(PaymentMethods[j], DailyPaymentTotals[i][j]);
          jDayData.Add('payments', jPayments);

          jDailyBreakdown.Add(jDayData);
        end;

        // Add arrays to main object
        jData.Add('dates', jDays);
        jData.Add('amounts', jAmounts);
        jData.Add('discounts', jDiscounts);
        jData.Add('cash', jCash);
        jData.Add('nonCash', jNonCash);
        jData.Add('paymentMethods', jPaymentMethods);
        jData.Add('dailyBreakdown', jDailyBreakdown);

        // Format the JSON with nice indentation
        Result := jData.FormatJSON([], 2);
      except
        jDays.Free;
        jAmounts.Free;
        jDiscounts.Free;
        jCash.Free;
        jNonCash.Free;
        jPaymentMethods.Free;
        jDailyBreakdown.Free;
        raise;
      end;
    finally
      jData.Free;
    end;
  finally
    PaymentMethods.Free;
  end;
end;

function TSnpReport.GenerateHourlyData(Year, Month: Integer): string;
var
  Sessions: TSessionArray;
  HourlyTotals: array[0..23] of Double;
  HourlyCounts: array[0..23] of Integer;
  i, Hour: Integer;
  H, M, S, MS: Word;
  HourlyAverages: array[0..23] of Double;
  
  // JSON objects
  jData: TJSONObject;
  jHours: TJSONArray;
  jHour: TJSONObject;
begin
  Sessions := FDatabase.GetSessionsByMonth(Year, Month);
  
  // Initialize arrays
  for i := 0 to 23 do
  begin
    HourlyTotals[i] := 0;
    HourlyCounts[i] := 0;
  end;
  
  // Calculate hourly totals and counts
  for i := 0 to High(Sessions) do
  begin
    DecodeTime(Sessions[i].StartTime, H, M, S, MS);
    Hour := H;
    HourlyTotals[Hour] := HourlyTotals[Hour] + Sessions[i].FinalAmount;
    Inc(HourlyCounts[Hour]);
  end;
  
  // Calculate hourly averages
  for i := 0 to 23 do
  begin
    if HourlyCounts[i] > 0 then
      HourlyAverages[i] := HourlyTotals[i] / HourlyCounts[i]
    else
      HourlyAverages[i] := 0;
  end;
  
  // Build JSON structure
  jData := TJSONObject.Create;
  try
    jHours := TJSONArray.Create;
    
    // Add hourly data to array
    for i := 0 to 23 do
    begin
      jHour := TJSONObject.Create;
      try
        jHour.Add('hour', i);
        jHour.Add('total', HourlyTotals[i]);
        jHour.Add('count', HourlyCounts[i]);
        jHour.Add('average', HourlyAverages[i]);
        
        jHours.Add(jHour);
      except
        jHour.Free;
        raise;
      end;
    end;
    
    // Add hours array to main object
    jData.Add('hours', jHours);
    
    // Format the JSON with nice indentation
    Result := jData.FormatJSON([], 2);
  finally
    jData.Free;
  end;
end;

function CompareIntegers(List: TStringList; Index1, Index2: Integer): Integer;
var
  Val1, Val2: Integer;
begin
  Val1 := StrToIntDef(List[Index1], 0);
  Val2 := StrToIntDef(List[Index2], 0);
  Result := Val1 - Val2;
end;

function CompareJSONIntegers(Item1, Item2: Pointer): Integer;
var
  Obj1, Obj2: TJSONData;
begin
  Obj1 := TJSONData(Item1);
  Obj2 := TJSONData(Item2);
  Result := Obj1.AsInteger - Obj2.AsInteger;
end;

procedure TSnpReport.UpdateReportsList(const OutputDir: string = '');
var
  Reports: TJSONObject;
  YearlyArray, MonthlyArray: TJSONArray;
  YearObj: TJSONObject;
  SearchRec: TSearchRec;
  Year, Month: Integer;
  YearStr, MonthStr: string;
  i, j: Integer;
  Months: TStringList;
  YearData: TJSONArray;
  ReportsPath: string;
begin
  // Use provided OutputDir or fall back to FReportsPath
  if OutputDir <> '' then
    ReportsPath := IncludeTrailingPathDelimiter(OutputDir)
  else
    ReportsPath := IncludeTrailingPathDelimiter(FReportsPath);

  WriteLn('Debug: UpdateReportsList - ReportsPath: ', ReportsPath);
  Reports := TJSONObject.Create;
  try
    YearlyArray := TJSONArray.Create;
    YearObj := TJSONObject.Create;
    
    // Find all yearly reports
    WriteLn('Debug: Looking for yearly reports in: ', ReportsPath + 'yearly_report_*.html');
    if FindFirst(ReportsPath + 'yearly_report_*.html', faAnyFile, SearchRec) = 0 then
    begin
      repeat
        if (SearchRec.Name <> '.') and (SearchRec.Name <> '..') and
           (Pos('yearly_report_', SearchRec.Name) = 1) then
        begin
          WriteLn('Debug: Found yearly report: ', SearchRec.Name);
          // Extract year from filename
          YearStr := Copy(SearchRec.Name, 15, 4); // 'yearly_report_YYYY.html'
          if TryStrToInt(YearStr, Year) then
            YearlyArray.Add(Year);
        end;
      until FindNext(SearchRec) <> 0;
      FindClose(SearchRec);
    end
    else
      WriteLn('Debug: No yearly reports found');
    
    // Sort years in descending order
    for i := 0 to YearlyArray.Count - 1 do
      for j := i + 1 to YearlyArray.Count - 1 do
        if YearlyArray.Integers[i] < YearlyArray.Integers[j] then
          YearlyArray.Exchange(i, j);
    
    // Find all monthly reports
    Months := TStringList.Create;
    try
      if FindFirst(ReportsPath + 'monthly_report_*.html', faAnyFile, SearchRec) = 0 then
      begin
        repeat
          if (SearchRec.Name <> '.') and (SearchRec.Name <> '..') and
             (Pos('monthly_report_', SearchRec.Name) = 1) and (Length(SearchRec.Name) >= 21) then
          begin
            // Extract year and month from filename
            YearStr := Copy(SearchRec.Name, 16, 4);  // 'monthly_report_YYYY_MM.html'
            MonthStr := Copy(SearchRec.Name, 21, 2);
            if TryStrToInt(YearStr, Year) and TryStrToInt(MonthStr, Month) then
            begin
              // Add to months list for sorting
              Months.Add(Format('%.4d-%.2d', [Year, Month]));
            end;
          end;
        until FindNext(SearchRec) <> 0;
        FindClose(SearchRec);
      end;
      
      // Sort months
      Months.CustomSort(@CompareIntegers);
      
      // Group months by year
      for i := 0 to Months.Count - 1 do
      begin
        YearStr := Copy(Months[i], 1, 4);
        MonthStr := Copy(Months[i], 6, 2);
        
        if not YearObj.Find(YearStr, YearData) then
        begin
          YearData := TJSONArray.Create;
          YearObj.Add(YearStr, YearData);
        end;
        YearData.Add(StrToInt(MonthStr));
      end;
    finally
      Months.Free;
    end;
    
    // Add the yearly report indicator (0) to months for years that have yearly reports
    for i := 0 to YearlyArray.Count - 1 do
    begin
      YearStr := IntToStr(YearlyArray.Integers[i]);
      if not YearObj.Find(YearStr, YearData) then
      begin
        YearData := TJSONArray.Create;
        YearObj.Add(YearStr, YearData);
      end;
      
      // Remove any existing 0 entry to avoid duplicates
      j := 0;
      while j < YearData.Count do
      begin
        if YearData.Items[j].AsInteger = 0 then
          YearData.Delete(j)
        else
          Inc(j);
      end;
      
      // Insert 0 at the beginning
      YearData.Insert(0, TJSONIntegerNumber.Create(0));
    end;
    
    // Build the final JSON
    Reports.Add('lastUpdated', FormatDateTime('yyyy-mm-dd"T"hh:nn:ss.zzz"Z"', Now));
    Reports.Add('yearly', YearlyArray);
    Reports.Add('monthly', YearObj);
    
    // Save to file
    WriteLn('Debug: Saving reports.json to: ', ReportsPath + 'reports.json');
    WriteLn('Debug: Yearly reports count: ', YearlyArray.Count);
    with TStringList.Create do
    try
      Text := Reports.AsJSON;
      SaveToFile(ReportsPath + 'reports.json');
      WriteLn('Debug: reports.json saved successfully');
    finally
      Free;
    end;
  finally
    Reports.Free;
  end;
end;

end.
