# SnP Report Generator

A console application that generates HTML reports from Firebird database data, featuring interactive charts using Chart.js.

## Features

- Generate yearly sales reports with monthly breakdowns
- Generate monthly reports with daily and hourly sales analysis
- Interactive charts for better data visualization
- Export to HTML for easy sharing and viewing in any web browser

## Prerequisites

- Free Pascal Compiler (FPC) 3.2.2 or later
- Lazarus IDE (optional, for development)
- Firebird 3.0 or later
- Access to the SESSIONS.FDB database

## Installation

1. Clone or download this repository
2. Ensure you have the required dependencies installed
3. Update the database connection settings in the code if needed

## Building the Application

### Using Lazarus IDE

1. Open the `ReportGenerator.lpi` file in Lazarus
2. Click "Run" > "Build" or press Ctrl+F9

### Using Command Line

Run the provided batch file:

```bash
build_and_run.bat
```

Or compile manually:

```bash
fpc -FEbin -Fu. -FuC:\path\to\lazarus\fpc\3.2.2\units\x86_64-win64\rtl -FuC:\path\to\lazarus\fpc\3.2.2\units\x86_64-win64\fcl-base -FuC:\path\to\lazarus\fpc\3.2.2\units\x86_64-win64\fcl-json -FuC:\path\to\lazarus\fpc\3.2.2\units\x86_64-win64\fcl-xml -FuC:\path\to\lazarus\fpc\3.2.2\units\x86_64-win64\fcl-process -FuC:\path\to\lazarus\fpc\3.2.2\units\x86_64-win64\ibase -MObjFPC -Scghi -Cg -g -gl -l -vewnhibq -Fi. ReportGenerator.dpr
```

## Usage

### Command Line Options

```
ReportGenerator [options]

Options:
  --year YYYY      Generate report for the specified year (required)
  --month MM       Generate monthly report (requires --year)
  --output DIR     Output directory (default: ./reports)
  --help           Show this help message
```

### Examples

Generate a yearly report for 2023:
```
ReportGenerator --year 2023
```

Generate a monthly report for January 2023:
```
ReportGenerator --year 2023 --month 1
```

Specify a custom output directory:
```
ReportGenerator --year 2023 --output C:\reports
```

## Output

Reports are generated as HTML files in the specified output directory:
- Yearly reports: `yearly_report_YYYY.html`
- Monthly reports: `monthly_report_YYYY_MM.html`

## Database Configuration

The application looks for a `snpconfig.ini` file in the same directory as the executable with the following format:

```ini
[Database]
NetworkPath=C:\path\to\SESSIONS.FDB
```

If the file doesn't exist, it will try to connect to a local Firebird database at `localhost:path/to/executable/SESSIONS.FDB`.

## Dependencies

- Firebird client library (fbclient.dll)
- Chart.js (loaded from CDN in generated HTML)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
