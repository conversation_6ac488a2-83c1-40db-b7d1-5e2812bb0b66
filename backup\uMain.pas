unit uMain;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils, uDB, uReports, DateUtils,
  zipper, Process, IniFiles, base64;

type
  { TReportApplication }
  TReportApplication = class
  private
    FDatabase: TSnpDatabase;
    FReport: TSnpReport;
    FOutputDir: String;
    FEmailRecipient: String;
    FSkipEmail: Boolean;
    procedure ShowHelp;
    function GetOutputDirectory: String;
    procedure GenerateReport(const Year: Integer; const Month: Integer = 0);
    function ZipReports(const SourceDir: String): String;
    procedure LoadSMTPConfig(out SMTPHost, SMTPUser, SMTPPass, SMTPFrom: String; out SMTPPort: Integer; out UseSSL: Boolean);
    procedure SendEmailWithAttachment(const ZipFilePath: String);
  public
    constructor Create; reintroduce;
    destructor Destroy; override;
    procedure Initialize;
    procedure Run;
  end;

implementation

constructor TReportApplication.Create;
begin
  inherited Create;
  FDatabase := TSnpDatabase.Create;
  FReport := TSnpReport.Create(FDatabase);
  FEmailRecipient := '<EMAIL>';
  FSkipEmail := False;
end;

destructor TReportApplication.Destroy;
begin
  FReport.Free;
  FDatabase.Free;
  inherited Destroy;
end;

procedure TReportApplication.Initialize;
begin
  // Initialize application settings
  WriteLn('SNP Report Generator - Console Application');
end;

procedure TReportApplication.ShowHelp;
begin
  WriteLn('Showing help screen');
  WriteLn('SNP Report Generator');
  WriteLn('--------------------');
  WriteLn('Usage: ', ExtractFileName(ParamStr(0)), ' [options]');
  WriteLn('');
  WriteLn('Options:');
  WriteLn('  --year YYYY      Generate yearly report for specified year');
  WriteLn('  --month MM       Generate monthly report (requires --year)');
  WriteLn('  --no-email       Skip email sending (just generate reports and ZIP)');
  WriteLn('  --help           Show this help');
  WriteLn('');
  WriteLn('Features:');
  WriteLn('  - Reports saved to SNP/reports directory (relative to database)');
  WriteLn('  - Automatically creates ZIP archive of generated reports');
  WriteLn('  - Emails ZIP <NAME_EMAIL> (unless --no-email is used)');
  WriteLn('');
  WriteLn('Examples:');
  WriteLn('  ', ExtractFileName(ParamStr(0)), ' --year 2025');
  WriteLn('  ', ExtractFileName(ParamStr(0)), ' --year 2025 --month 7');
end;

function TReportApplication.GetOutputDirectory: String;
var
  DatabasePath, DatabaseDir: String;
  i: Integer;
begin
  Result := '';
  
  // First check for command line parameter
  if ParamCount > 0 then
  begin
    for i := 1 to ParamCount - 1 do
    begin
      if (ParamStr(i) = '--output') and (i < ParamCount) then
      begin
        Result := ParamStr(i + 1);
        Exit;
      end;
    end;
  end;
  
  // If no command line parameter, use database-relative path
  try
    DatabasePath := FDatabase.GetDatabasePath;
    if DatabasePath <> '' then
    begin
      // Extract directory from database path and set up SNP/reports
      DatabaseDir := ExtractFilePath(DatabasePath);
      DatabaseDir := ExtractFilePath(ExcludeTrailingPathDelimiter(DatabaseDir)); // Go up from 'data' to 'SNP'
      Result := IncludeTrailingPathDelimiter(DatabaseDir) + 'reports';
    end
    else
    begin
      // Fallback to application directory if database path cannot be determined
      Result := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0))) + 'SNP' + PathDelim + 'reports';
      WriteLn('Warning: Could not determine database path, using fallback: ', Result);
    end;
  except
    on E: Exception do
    begin
      // Fallback to application directory on error
      Result := IncludeTrailingPathDelimiter(ExtractFilePath(ParamStr(0))) + 'SNP' + PathDelim + 'reports';
      WriteLn('Error getting database path: ', E.Message, ', using fallback: ', Result);
    end;
  end;
  
  // Final fallback if Result is still empty
  if Result = '' then
    Result := 'reports';
    
  FOutputDir := Result;
end;

procedure TReportApplication.GenerateReport(const Year: Integer; const Month: Integer);
var
  OutputDir: String;
  StartTime: TDateTime;
  ZipFilePath: String;
begin
  StartTime := Now;
  WriteLn('Starting report generation at ', FormatDateTime('yyyy-mm-dd hh:nn:ss', StartTime));
  
  // Get the proper output directory using database path
  OutputDir := GetOutputDirectory;
  
  WriteLn('Using output directory: ', OutputDir);
  ForceDirectories(OutputDir);
  
  try
    WriteLn('Connecting to database...');
    FDatabase.Connect;
    WriteLn('Successfully connected to database');
    
    if Month > 0 then
    begin
      WriteLn('Generating monthly report for ', Year, '-', Format('%.2d', [Month]), '...');
      FReport.GenerateMonthlyReport(Year, Month, OutputDir);
      WriteLn('Monthly report generated successfully');
    end
    else
    begin
      WriteLn('Generating yearly report for ', Year, '...');
      FReport.GenerateYearlyReport(Year, OutputDir);
      WriteLn('Yearly report generated successfully');
    end;
    
    // Zip the reports
    WriteLn('Creating zip archive...');
    ZipFilePath := ZipReports(OutputDir);
    
    if ZipFilePath <> '' then
    begin
      if FSkipEmail then
      begin
        WriteLn('Email sending skipped (--no-email option used)');
        WriteLn('ZIP file available at: ', ZipFilePath);
      end
      else
      begin
        // Send email with zip attachment
        WriteLn('Sending email with reports...');
        SendEmailWithAttachment(ZipFilePath);
      end;
    end
    else
    begin
      WriteLn('Failed to create zip file - email not sent');
    end;
    
    WriteLn('Report files saved to: ', ExpandFileName(OutputDir));
  except
    on E: Exception do
    begin
      WriteLn('ERROR: ', E.ClassName, ': ', E.Message);
      WriteLn('Report generation failed after ', FormatDateTime('n "minute(s)" s "second(s)"', Now - StartTime));
      Halt(1);
    end;
  end;
  
  WriteLn('Report generation completed in ', FormatDateTime('n "minute(s)" s "second(s)"', Now - StartTime));
end;

procedure TReportApplication.Run;
var
  i: Integer;
  Year, Month: Integer;
  Param: String;
  HasYear, HasMonth: Boolean;
begin
  Year := 0;
  Month := 0;
  HasYear := False;
  HasMonth := False;
  
  if ParamCount = 0 then
  begin
    ShowHelp;
    Exit;
  end;
  
  i := 1;
  while i <= ParamCount do
  begin
    Param := ParamStr(i);
    
    if (Param = '--help') or (Param = '-h') then
    begin
      ShowHelp;
      Exit;
    end
    else if (Param = '--year') and (i < ParamCount) then
    begin
      Inc(i);
      if TryStrToInt(ParamStr(i), Year) then
        HasYear := True
      else
      begin
        WriteLn('Invalid year: ', ParamStr(i));
        Exit;
      end;
    end
    else if (Param = '--month') and (i < ParamCount) then
    begin
      Inc(i);
      if TryStrToInt(ParamStr(i), Month) then
        HasMonth := True
      else
      begin
        WriteLn('Invalid month: ', ParamStr(i));
        Exit;
      end;
    end
    else if (Param = '--output') and (i < ParamCount) then
    begin
      Inc(i);
      // WriteLn('Output directory parameter found, value: ', ParamStr(i));
    end
    else if Param = '--no-email' then
    begin
      FSkipEmail := True;
      // WriteLn('Email sending disabled');
    end;
    Inc(i);
  end;
  
  if not HasYear then
  begin
    WriteLn('Error: --year parameter is required');
    WriteLn('Use --help for usage information');
    Exit;
  end;
  
  if HasMonth and ((Month < 1) or (Month > 12)) then
  begin
    WriteLn('Error: Month must be between 1 and 12');
    Exit;
  end;
  
  try
    GenerateReport(Year, Month);
  except
    on E: Exception do
    begin
      WriteLn('Fatal error: ', E.Message);
      Halt(1);
    end;
  end;
end;

function TReportApplication.ZipReports(const SourceDir: String): String;
var
  Zipper: TZipper;
  SearchRec: TSearchRec;
  ZipFileName: String;
  FileList: TStringList;
  i: Integer;
begin
  Result := '';
  ZipFileName := IncludeTrailingPathDelimiter(ExtractFilePath(SourceDir)) +
                 'SNP_Reports_' + FormatDateTime('yyyy-mm-dd_hh-nn-ss', Now) + '.zip';

  FileList := TStringList.Create;
  Zipper := TZipper.Create;
  try
    // Find all HTML files in the reports directory
    try
      if SysUtils.FindFirst(IncludeTrailingPathDelimiter(SourceDir) + '*.html', faAnyFile, SearchRec) = 0 then
      begin
        repeat
          if (SearchRec.Attr and faDirectory) = 0 then  // Not a directory
            FileList.Add(IncludeTrailingPathDelimiter(SourceDir) + SearchRec.Name);
        until SysUtils.FindNext(SearchRec) <> 0;
        SysUtils.FindClose(SearchRec);
      end;
    except
      on E: Exception do
        WriteLn('Error searching for HTML files: ', E.Message);
    end;

    if FileList.Count > 0 then
    begin
      Zipper.FileName := ZipFileName;

      // Add files to zip
      for i := 0 to FileList.Count - 1 do
      begin
        Zipper.Entries.AddFileEntry(FileList[i], ExtractFileName(FileList[i]));
        WriteLn('Added to zip: ', ExtractFileName(FileList[i]));
      end;

      Zipper.ZipAllFiles;
      Result := ZipFileName;
      WriteLn('Created zip file: ', ZipFileName);
    end
    else
    begin
      WriteLn('No HTML files found to zip in: ', SourceDir);
    end;

  except
    on E: Exception do
    begin
      WriteLn('Error creating zip file: ', E.Message);
      Result := '';
    end;
  end;

  FileList.Free;
  Zipper.Free;
end;

procedure TReportApplication.LoadSMTPConfig(out SMTPHost, SMTPUser, SMTPPass, SMTPFrom: String; out SMTPPort: Integer; out UseSSL: Boolean);
var
  ConfigFile: TIniFile;
  ConfigPath: String;
begin
  // Default values
  SMTPHost := 'smtp.gmail.com';
  SMTPPort := 587;
  UseSSL := True;
  SMTPUser := '';
  SMTPPass := '';
  SMTPFrom := '';

  // Try to load from config.ini
  ConfigPath := ExtractFilePath(ParamStr(0)) + 'config.ini';

  if FileExists(ConfigPath) then
  begin
    ConfigFile := TIniFile.Create(ConfigPath);
    try
      SMTPHost := ConfigFile.ReadString('SMTP', 'Host', SMTPHost);
      SMTPPort := ConfigFile.ReadInteger('SMTP', 'Port', SMTPPort);
      UseSSL := ConfigFile.ReadBool('SMTP', 'UseSSL', UseSSL);
      SMTPUser := ConfigFile.ReadString('SMTP', 'Username', SMTPUser);
      SMTPPass := ConfigFile.ReadString('SMTP', 'Password', SMTPPass);
      SMTPFrom := ConfigFile.ReadString('SMTP', 'FromEmail', SMTPFrom);

      // If FromEmail is not set, use Username as default
      if SMTPFrom = '' then
        SMTPFrom := SMTPUser;

    finally
      ConfigFile.Free;
    end;
  end
  else
  begin
    // Create a sample config file with instructions
    ConfigFile := TIniFile.Create(ConfigPath);
    try
      ConfigFile.WriteString('SMTP', 'Host', SMTPHost);
      ConfigFile.WriteInteger('SMTP', 'Port', SMTPPort);
      ConfigFile.WriteBool('SMTP', 'UseSSL', UseSSL);
      ConfigFile.WriteString('SMTP', 'Username', '');
      ConfigFile.WriteString('SMTP', 'Password', '');
      ConfigFile.WriteString('SMTP', 'FromEmail', '');

      // Add comments section
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Host', 'SMTP server (e.g., smtp.gmail.com, smtp.outlook.com)');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Port', '587 for TLS, 465 for SSL, 25 for plain');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'UseSSL', 'true for secure connection, false for plain');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Username', 'Your email address');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Password', 'Your email password or app password');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'FromEmail', 'Email address to send from (usually same as Username)');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Gmail_Note', 'For Gmail use App Password, not regular password');
      ConfigFile.WriteString('SMTP_INSTRUCTIONS', 'Outlook_Note', 'For Outlook use smtp.outlook.com port 587');

    finally
      ConfigFile.Free;
    end;

    WriteLn('Created sample config file: ', ConfigPath);
    WriteLn('Please edit the [SMTP] section with your email credentials.');
  end;
end;

procedure TReportApplication.SendEmailWithAttachment(const ZipFilePath: String);
var
  SMTP: TSMTPSend;
  Msg: TMimeMess;
  Part: TMimePart;
  Subject, Body: String;
  SMTPHost, SMTPUser, SMTPPass, SMTPFrom: String;
  SMTPPort: Integer;
  UseSSL: Boolean;
begin
  if not FileExists(ZipFilePath) then
  begin
    WriteLn('Zip file not found: ', ZipFilePath);
    Exit;
  end;

  WriteLn('Loading SMTP configuration...');
  LoadSMTPConfig(SMTPHost, SMTPUser, SMTPPass, SMTPFrom, SMTPPort, UseSSL);

  // Check if SMTP is configured
  if (SMTPUser = '') or (SMTPPass = '') then
  begin
    WriteLn('SMTP credentials not configured in config.ini');
    WriteLn('Please edit config.ini and add your SMTP settings in the [SMTP] section.');
    WriteLn('ZIP file is available at: ', ZipFilePath);
    Exit;
  end;

  WriteLn('Sending email via Synapse SMTP...');
  WriteLn('SMTP Server: ', SMTPHost, ':', SMTPPort);
  WriteLn('From: ', SMTPFrom);
  WriteLn('To: ', FEmailRecipient);
  WriteLn('SSL/TLS: ', BoolToStr(UseSSL, True));

  try
    Subject := 'SNP Reports - ' + FormatDateTime('yyyy-mm-dd', Now);
    Body := 'Dear Recipient,' + #13#10#13#10 +
            'Please find attached the latest SNP reports generated on ' +
            FormatDateTime('yyyy-mm-dd hh:nn:ss', Now) + '.' + #13#10#13#10 +
            'The attached ZIP file contains:' + #13#10 +
            '- Monthly and/or yearly reports in HTML format' + #13#10 +
            '- Generated automatically by SNP Report Generator' + #13#10#13#10 +
            'Best regards,' + #13#10 +
            'SNP Report System';

    SMTP := TSMTPSend.Create;
    Msg := TMimeMess.Create;
    try
      // Configure SMTP
      SMTP.TargetHost := SMTPHost;
      SMTP.TargetPort := IntToStr(SMTPPort);
      SMTP.Username := SMTPUser;
      SMTP.Password := SMTPPass;

      if UseSSL then
      begin
        if SMTPPort = 465 then
          SMTP.FullSSL := True  // SSL
        else
          SMTP.AutoTLS := True; // TLS (STARTTLS)
      end;

      // Create message
      Msg.Header.From := SMTPFrom;
      Msg.Header.ToList.Add(FEmailRecipient);
      Msg.Header.Subject := Subject;
      Msg.Header.Date := Now;
      Msg.Header.MessageID := '<' + FormatDateTime('yyyymmddhhnnss', Now) + '@snpreport>';

      // Add body as multipart
      Part := Msg.AddPartMultipart('mixed', nil);
      Msg.AddPartText(Body, Part);

      // Add ZIP attachment
      WriteLn('Adding attachment: ', ExtractFileName(ZipFilePath));
      Msg.AddPartBinaryFromFile(ZipFilePath, Part);

      // Send email
      WriteLn('Connecting to SMTP server...');
      if SMTP.Login then
      begin
        WriteLn('Connected successfully. Sending email...');

        if SMTP.MailFrom(SMTPFrom, Length(Msg.Lines.Text)) then
        begin
          if SMTP.MailTo(FEmailRecipient) then
          begin
            if SMTP.MailData(Msg.Lines) then
            begin
              WriteLn('Email sent successfully to: ', FEmailRecipient);
              WriteLn('Subject: ', Subject);
              WriteLn('Attachment: ', ExtractFileName(ZipFilePath));
            end
            else
            begin
              WriteLn('Failed to send email data');
              WriteLn('SMTP Error: ', SMTP.ResultString);
            end;
          end
          else
          begin
            WriteLn('Failed to set recipient: ', FEmailRecipient);
            WriteLn('SMTP Error: ', SMTP.ResultString);
          end;
        end
        else
        begin
          WriteLn('Failed to set sender: ', SMTPFrom);
          WriteLn('SMTP Error: ', SMTP.ResultString);
        end;

        SMTP.Logout;
      end
      else
      begin
        WriteLn('Failed to connect to SMTP server: ', SMTPHost, ':', SMTPPort);
        WriteLn('SMTP Error: ', SMTP.ResultString);
        WriteLn('Please check:');
        WriteLn('  - SMTP server address and port');
        WriteLn('  - Username and password');
        WriteLn('  - Internet connection');
        WriteLn('  - Firewall settings');
        if UseSSL then
          WriteLn('  - SSL/TLS support (try port 587 for TLS or 465 for SSL)');
      end;

    finally
      Msg.Free;
      SMTP.Free;
    end;

  except
    on E: Exception do
    begin
      WriteLn('Error sending email: ', E.Message);
      WriteLn('ZIP file is available at: ', ZipFilePath);
      WriteLn('Please check your SMTP configuration in config.ini');
    end;
  end;
end;

end.
