program snpreport;

{$mode objfpc}{$H+}

uses
  {$IFDEF UNIX}
  cthreads,
  {$ENDIF}
  Classes, SysUtils, uMain, uDB, uReports;

//{$R *.res}  // Temporarily disabled to resolve build issues

begin
  try
    // Initialize and run the SNP Report Application
    with TReportApplication.Create do
    try
      Initialize;
      Run;
    finally
      Free;
    end;
  except
    on E: Exception do
    begin
      WriteLn('Fatal error: ', E.Message);
      ExitCode := 1;
    end;
  end;
end.
