<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnP Reports Dashboard</title>
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"></noscript>
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
            visibility: hidden; /* Prevent flash of unstyled content */
        }
        body.loaded {
            visibility: visible;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .dashboard-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .report-header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .report-container {
            margin-top: 30px;
            min-height: 500px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background: #fff;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner-border {
            width: 3rem; 
            height: 3rem;
        }
        .report-iframe {
            width: 100%;
            height: 100%;
            min-height: 600px;
            border: none;
            border-radius: 5px;
        }
        .instructions {
            background-color: #f8f9fa;
            border-left: 4px solid #0d6efd;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }
    </style>
    <!-- Fallback for when JavaScript is disabled -->
    <noscript>
        <style>
            body { visibility: visible !important; }
        </style>
    </noscript>
</head>
<body>
    <div class="dashboard-container">
        <div class="report-header">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>SnP Reports Dashboard</h1>
                <button id="sign-out-button" class="btn btn-outline-danger">
                    <i class="bi bi-box-arrow-right"></i> Sign Out
                </button>
            </div>
           <!-- (instructions) 
            <div class="instructions">
                <h5>How to use:</h5>
                <ol>
                    <li>Select a year and month from the dropdown menus</li>
                    <li>Click "View Report" to load the selected report</li>
                    <li>Select "All Months" to view the yearly report</li>
                </ol>
            </div>
            -->
            
            <form id="reportForm" class="row g-3">
                <div class="col-md-5">
                    <label for="yearSelect" class="form-label">Select Year</label>
                    <select class="form-select" id="yearSelect" required>
                        <!-- Will be populated by JavaScript -->
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="monthSelect" class="form-label">Select Month</label>
                    <select class="form-select" id="monthSelect" required>
                        <option value="0">All Months (Yearly Report)</option>
                        <option value="1">January</option>
                        <option value="2">February</option>
                        <option value="3">March</option>
                        <option value="4">April</option>
                        <option value="5">May</option>
                        <option value="6">June</option>
                        <option value="7">July</option>
                        <option value="8">August</option>
                        <option value="9">September</option>
                        <option value="10">October</option>
                        <option value="11">November</option>
                        <option value="12">December</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">View Report</button>
                </div>
            </form>
        </div>

        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading report, please wait...</p>
        </div>

        <div class="report-container" id="reportContainer">
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="text-center text-muted">
                    <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                    <h4>No report selected</h4>
                    <p>Please select a year and month above to view a report.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Firebase first -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>

    <!-- Other dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <script>
        // Initialize Firebase
        if (typeof firebase !== 'undefined' && firebase.apps.length === 0) {
            const firebaseConfig = {
                apiKey: "AIzaSyDbZq0cj8MoegdQcOKyrjXnWGSbRQl8Ito",
                authDomain: "snpweb-667b7.firebaseapp.com",
                projectId: "snpweb-667b7",
                storageBucket: "snpweb-667b7.firebasestorage.app",
                messagingSenderId: "1010342767989",
                appId: "1:1010342767989:web:95c3c40944cbb728a0773c"
            };
            firebase.initializeApp(firebaseConfig);
        }

        // Get auth instance
        const auth = firebase.auth();

        // Sign out function
        document.addEventListener('DOMContentLoaded', function() {
            const signOutButton = document.getElementById('sign-out-button');
            if (signOutButton) {
                signOutButton.addEventListener('click', () => {
                    auth.signOut().then(() => {
                        console.log('User signed out');
                        window.location.href = 'index.html';
                    }).catch((error) => {
                        console.error('Sign out error:', error);
                    });
                });
            }
        });

        // Check auth state
        auth.onAuthStateChanged((user) => {
            if (!user) {
                // User is not signed in, redirect to login page
                window.location.href = 'index.html';
            }
        });

        // Wait for all resources including stylesheets to load
        function waitForStylesheets() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve();
                    return;
                }

                let attempts = 0;
                const maxAttempts = 300; // 3 seconds max wait

                // Check if all stylesheets are loaded
                const checkStylesheets = () => {
                    attempts++;

                    // Fallback: resolve after max attempts to prevent infinite waiting
                    if (attempts >= maxAttempts) {
                        console.warn('Stylesheets loading timeout, proceeding anyway');
                        resolve();
                        return;
                    }

                    const stylesheets = document.querySelectorAll('link[rel="stylesheet"], link[rel="preload"][as="style"]');
                    let allLoaded = true;

                    stylesheets.forEach(link => {
                        if (!link.sheet && link.rel === 'stylesheet') {
                            allLoaded = false;
                        }
                    });

                    if (allLoaded || document.readyState === 'complete') {
                        resolve();
                    } else {
                        setTimeout(checkStylesheets, 10);
                    }
                };

                checkStylesheets();
            });
        }

        // Function to get all available reports from reports.json
        async function getAvailableReports() {
            try {
                const response = await fetch('reports/reports.json');
                if (!response.ok) {
                    throw new Error('Failed to load reports list');
                }
                const data = await response.json();
                
                // Transform the data to match the expected format
                const years = [...data.yearly];
                const months = { ...data.monthly };
                
                // Ensure all years with monthly reports are included
                Object.keys(months).forEach(year => {
                    const yearNum = parseInt(year, 10);
                    if (!years.includes(yearNum)) {
                        years.push(yearNum);
                    }
                });
                
                // Sort years in descending order
                years.sort((a, b) => b - a);
                
                return { years, months };
            } catch (error) {
                console.error('Error loading reports list:', error);
                // Show NO DATA message
                document.getElementById('reportContainer').innerHTML = `
                    <div class="alert alert-warning mt-4" role="alert">
                        <h4 class="alert-heading">NO DATA</h4>
                        <p>No report data is available. Please generate some reports first.</p>
                    </div>
                `;
                // Return empty data to prevent further processing
                return { years: [], months: {} };
            }
        }

        document.addEventListener('DOMContentLoaded', async function() {
            // Wait for stylesheets to load before proceeding
            await waitForStylesheets();

            // Show the body now that stylesheets are loaded
            document.body.classList.add('loaded');

            const yearSelect = document.getElementById('yearSelect');
            const monthSelect = document.getElementById('monthSelect');
            const currentYear = new Date().getFullYear();
            const currentMonth = new Date().getMonth() + 1;
            
            // Add loading option
            yearSelect.innerHTML = '<option value="" disabled>Loading available reports...</option>';
            monthSelect.disabled = true;
            
            // Get available reports
            const { years, months } = await getAvailableReports();
            
            // Clear loading message
            yearSelect.innerHTML = '';
            
            if (years.length === 0) {
                yearSelect.innerHTML = '<option value="" disabled>No reports found</option>';
                return;
            }
            
            // Populate year select with available years
            years.sort((a, b) => b - a); // Sort years in descending order
            let hasCurrentYear = false;
            
            years.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                if (year === currentYear) {
                    option.selected = true;
                    hasCurrentYear = true;
                }
                yearSelect.appendChild(option);
            });
            
            // If current year not available, select first available year
            if (!hasCurrentYear) {
                yearSelect.value = years[0];
            }
            
            // Update month options based on selected year
            function updateMonthOptions() {
                const selectedYear = parseInt(yearSelect.value);
                const availableMonths = months[selectedYear] || [];
                
                // Clear and disable month select
                monthSelect.innerHTML = '';
                monthSelect.disabled = availableMonths.length === 0;
                
                if (availableMonths.length === 0) {
                    monthSelect.innerHTML = '<option value="" disabled>No monthly reports available</option>';
                    return;
                }
                
                // Add yearly report option if available
                if (availableMonths.includes(0)) {
                    const yearlyOption = document.createElement('option');
                    yearlyOption.value = '0';
                    yearlyOption.textContent = 'All Months (Yearly Report)';
                    monthSelect.appendChild(yearlyOption);
                }
                
                // Add month options
                const monthNames = [
                    'January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ];
                
                availableMonths.forEach(month => {
                    if (month !== 0) { // Skip yearly report (0)
                        const option = document.createElement('option');
                        option.value = month;
                        option.textContent = monthNames[month - 1];
                        
                        // Select current month if available and current year is selected
                        if (selectedYear === currentYear && month === currentMonth) {
                            option.selected = true;
                        }
                        
                        monthSelect.appendChild(option);
                    }
                });
                
                // If no month is selected, select the first available month
                //if (!monthSelect.value && monthSelect.options.length > 0) {
                    monthSelect.selectedIndex = 0;
                //}
            }
            
            // Initial month options update
            updateMonthOptions();
            
            // Update month options when year changes
            yearSelect.addEventListener('change', updateMonthOptions);

            // Handle form submission
            document.getElementById('reportForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const year = document.getElementById('yearSelect').value;
                const month = document.getElementById('monthSelect').value;
                
                loadReport(year, month);
            });

            // Function to load the selected report
            function loadReport(year, month) {
                const reportContainer = document.getElementById('reportContainer');
                const loadingSpinner = document.getElementById('loadingSpinner');
                
                // Show loading spinner
                loadingSpinner.style.display = 'block';
                reportContainer.innerHTML = '';
                
                // Determine the report filename based on selection
                let reportFile;
                if (month === '0') {
                    reportFile = `reports/yearly_report_${year}.html`;
                } else {
                    reportFile = `reports/monthly_report_${year}_${month.toString().padStart(2, '0')}.html`;
                }
                
                // Create iframe to display the report
                const iframe = document.createElement('iframe');
                iframe.className = 'report-iframe';
                iframe.onload = function() {
                    loadingSpinner.style.display = 'none';
                };
                iframe.onerror = function() {
                    loadingSpinner.style.display = 'none';
                    reportContainer.innerHTML = `
                        <div class="alert alert-warning mt-4" role="alert">
                            <h4 class="alert-heading">NO DATA</h4>
                            <p>The selected report is not available. Please generate this report first.</p>
                        </div>
                    `;
                };
                
                iframe.src = reportFile;
                reportContainer.appendChild(iframe);
            }
            
            // Load current month's report by default after a small delay
            // This prevents forced layout before page is fully rendered
            setTimeout(() => {
                if (years.length > 0 && monthSelect.options.length > 0) {
                    const selectedYear = yearSelect.value || currentYear;
                    const selectedMonth = monthSelect.value || currentMonth;
                    loadReport(selectedYear, selectedMonth);
                }
            }, 100);
        });
    </script>

    <!-- Main application script -->
    <script>
        // Check if user is authenticated
        if (typeof firebase !== 'undefined') {
            const auth = firebase.auth();
            auth.onAuthStateChanged((user) => {
                if (!user) {
                    window.location.href = 'index.html';
                }
            });
        } else {
            console.error('Firebase not loaded');
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
