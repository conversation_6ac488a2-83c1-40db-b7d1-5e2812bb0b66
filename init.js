// Import Firebase modules from CDN
import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
import { 
    getAuth, 
    RecaptchaVerifier, 
    signInWithPhoneNumber,
    PhoneAuthProvider
} from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDbZq0cj8MoegdQcOKyrjXnWGSbRQl8Ito",
  authDomain: "snpweb-667b7.firebaseapp.com",
  projectId: "snpweb-667b7",
  storageBucket: "snpweb-667b7.firebasestorage.app",
  messagingSenderId: "1010342767989",
  appId: "1:1010342767989:web:95c3c40944cbb728a0773c"
};

// Initialize Firebase
export const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Export auth and other utilities for use in other files
export { 
    auth, 
    RecaptchaVerifier, 
    signInWithPhoneNumber,
    PhoneAuthProvider 
};

// Make firebaseui available globally
window.firebase = { auth };
  
  // Step 2: Handle sending the verification code
  async function sendVerificationCode() {
    const phoneNumber = document.getElementById('phone-number-input').value; // Get phone number from an input field
    const appVerifier = window.recaptchaVerifier;
  
    try {
      const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, appVerifier);
      window.confirmationResult = confirmationResult; // Save the confirmationResult for later
      console.log("Verification code sent!");
      // At this point, prompt the user to enter the code they received.
      document.getElementById('verification-code-section').style.display = 'block'; // Show the input field for the code
    } catch (error) {
      console.error("Error sending verification code:", error);
      // Handle specific errors, e.g., invalid phone number, reCAPTCHA failed
      // You might need to reset the reCAPTCHA if it was invisible
      appVerifier.render().then(function (widgetId) {
        grecaptcha.reset(widgetId);
      });
    }
  }
  
  // Step 3: Handle verifying the code and signing in
  async function verifyCodeAndSignIn() {
    const code = document.getElementById('verification-code-input').value; // Get the code from an input field
    try {
      const result = await window.confirmationResult.confirm(code);
      // User signed in successfully.
      const user = result.user;
      console.log("User signed in!", user);
      // You can now redirect the user or update your UI.
    } catch (error) {
      console.error("Error verifying code:", error);
      // User couldn't sign in (bad verification code?)
      // You might want to display an error message to the user.
    }
  }
  
  // Example of how you might trigger these functions with buttons
  // <button onclick="sendVerificationCode()">Send Code</button>
  // <input type="text" id="phone-number-input" placeholder="+1234567890">
  // <div id="recaptcha-container"></div> // Important: This div is where the reCAPTCHA renders
  
  // <div id="verification-code-section" style="display:none;">
  //   <input type="text" id="verification-code-input" placeholder="Enter verification code">
  //   <button onclick="verifyCodeAndSignIn()">Verify & Sign In</button>
  // </div>

  