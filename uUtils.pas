unit uUtils;

{$mode objfpc}{$H+}

interface

uses
  Classes, SysUtils;

function EscapeJSON(const Str: string): string;
function FormatCurrency(Amount: Double): string;

implementation

function EscapeJSON(const Str: string): string;
begin
  Result := StringReplace(Str, '\', '\\', [rfReplaceAll]);
  Result := StringReplace(Result, '"', '\"', [rfReplaceAll]);
  Result := StringReplace(Result, #13#10, '\n', [rfReplaceAll]);
  Result := StringReplace(Result, #10, '\n', [rfReplaceAll]);
  Result := StringReplace(Result, #13, '\n', [rfReplaceAll]);
  Result := StringReplace(Result, #9, '\t', [rfReplaceAll]);
  Result := StringReplace(Result, #8, '\b', [rfReplaceAll]);
  Result := StringReplace(Result, #12, '\f', [rfReplaceAll]);
end;

function FormatCurrency(Amount: Double): string;
begin
  Result := FormatFloat('0.00', Amount);
end;

end.
