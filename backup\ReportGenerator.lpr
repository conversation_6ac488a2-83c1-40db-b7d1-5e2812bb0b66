program ReportGenerator;

{$mode objfpc}{$H+}

uses
  {$IFDEF UNIX}
  cthreads,
  {$ENDIF}
  Classes, SysUtils, uMain, uDB, uReports;



begin
  try
    // Initialize and run the application
    with TReportApplication.Create(nil) do
    try
      Initialize;
      Run;
    finally
      Free;
    end;
  except
    on E: Exception do
    begin
      WriteLn('Fatal error: ', E.Message);
      ExitCode := 1;
    end;
  end;
end.
