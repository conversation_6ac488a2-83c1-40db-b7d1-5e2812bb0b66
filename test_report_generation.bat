@echo off
echo Testing Report Generator with sample data...
echo.

REM Create a test output directory
set TEST_DIR=test_output
if not exist "%TEST_DIR%" mkdir "%TEST_DIR%"

REM Test 1: Generate yearly report
echo Generating yearly report for 2023...
bin\ReportGenerator.exe --year 2023 --output %TEST_DIR%
if %ERRORLEVEL% NEQ 0 (
    echo Error generating yearly report
    exit /b 1
)

REM Test 2: Generate monthly report
echo Generating monthly report for January 2023...
bin\ReportGenerator.exe --year 2023 --month 1 --output %TEST_DIR%
if %ERRORLEVEL% NEQ 0 (
    echo Error generating monthly report
    exit /b 1
)

echo.
echo Test reports generated in %CD%\%TEST_DIR% directory
echo.
echo Opening test reports in default browser...
start "" "%CD%\%TEST_DIR%\yearly_report_2023.html"
start "" "%CD%\%TEST_DIR%\monthly_report_2023_01.html"

echo.
echo Testing complete!
pause
